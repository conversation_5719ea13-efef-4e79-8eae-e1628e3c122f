import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export async function POST(request) {
  try {
    const data = await request.json();    // Konversi data form ke format yang sesuai dengan schema Prisma
    // FullCalendar expects end to be exclusive (not included), so add 1 day to endDate
    let end = null;
    if (data.endDate) {
      const endDateObj = new Date(data.endDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      end = new Date(`${endDateObj.toISOString().slice(0, 10)}T${data.endTime || '00:00'}`);
    } else if (data.startDate && data.endTime) {
      end = new Date(`${data.startDate}T${data.endTime}`);
    }
    const eventData = {
      id: uuidv4(), // Generate unique ID
      title: data.title,
      start: new Date(`${data.startDate}T${data.startTime || '00:00'}`),
      end,
      allDay: !data.startTime || !data.endTime,
      backgroundColor: data.backgroundColor || '#3788d8',
      borderColor: data.borderColor || '#3788d8',
      textColor: data.textColor || '#ffffff',
      description: data.description,
      // Simpan data tambahan
      extendedProps: JSON.stringify({
        location: data.location,
        organizer: data.organizer,
        isPublic: data.isPublic
      })
    };

    // Simpan ke database
    const event = await prisma.event.create({
      data: eventData
    });

    return NextResponse.json({
      success: true,
      event
    });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat menyimpan kegiatan'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const events = await prisma.event.findMany({
      orderBy: {
        start: 'asc'
      }
    });
    
    return NextResponse.json(events);
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json({
      success: false,
      error: 'Terjadi kesalahan saat mengambil data kegiatan'
    }, { status: 500 });
  }
}
