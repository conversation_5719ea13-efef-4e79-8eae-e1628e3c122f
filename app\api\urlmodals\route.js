import { prisma } from '../../lib/prisma';
import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';

export async function GET() {
  try {
    const items = await prisma.urlmodal.findMany({
      orderBy: { createdAt: 'desc' },
      select: { id: true, title: true, createdAt: true, updatedAt: true }
    });
    return NextResponse.json(items);
  } catch (e) {
    return NextResponse.json({ error: 'Gagal memuat data' }, { status: 500 });
  }
}

export async function POST(req) {
  try {
    const body = await req.json();
    const { title, content } = body || {};
    if (!title || !content) {
      return NextResponse.json({ error: 'Title dan content wajib diisi' }, { status: 400 });
    }
    const id = randomUUID();
    const created = await prisma.urlmodal.create({ data: { id, title, content } });
    return NextResponse.json(created, { status: 201 });
  } catch (e) {
    return NextResponse.json({ error: 'Gagal menyimpan' }, { status: 500 });
  }
}
