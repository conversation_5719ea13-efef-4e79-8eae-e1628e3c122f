// PWA Icon Generator - Creates proper icon sizes for PWA manifest
const fs = require('fs');
const path = require('path');

console.log('🎨 PWA Icon Size Fix');
console.log('===================');

// Check if logo.png exists
const logoPath = path.join(__dirname, 'public', 'logo.png');
if (!fs.existsSync(logoPath)) {
  console.error('❌ logo.png not found in public directory');
  process.exit(1);
}

// Read the PNG file to get dimensions
const buffer = fs.readFileSync(logoPath);
if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
  const width = buffer.readUInt32BE(16);
  const height = buffer.readUInt32BE(20);
  
  console.log(`📏 Current logo.png: ${width}x${height}px`);
  
  // Update manifest with correct size
  const manifestPath = path.join(__dirname, 'public', 'site.webmanifest');
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  
  // Update the logo icon entry
  const logoIcon = manifest.icons.find(icon => icon.src === '/logo.png');
  if (logoIcon) {
    logoIcon.sizes = `${width}x${width}`;
    console.log(`✅ Updated manifest icon size to ${logoIcon.sizes}`);
  }
  
  // Write updated manifest
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log('✅ Manifest updated successfully');
  
  console.log('\n📋 PWA Manifest is now fixed!');
  console.log('The icon size declaration now matches the actual image dimensions.');
  
} else {
  console.error('❌ Invalid PNG file format');
}
