# 🏛️ PPID BPMP Provinsi Kalimantan Timur

Portal Pejabat Pengelola Informasi dan Dokumentasi (PPID) BPMP Provinsi Kalimantan Timur - Sistem manajemen permohonan informasi publik yang modern dan user-friendly.

![PPID BPMP Kaltim](https://img.shields.io/badge/PPID-BPMP%20Kaltim-blue)
![Next.js](https://img.shields.io/badge/Next.js-14-black)
![React](https://img.shields.io/badge/React-18-blue)
![MySQL](https://img.shields.io/badge/MySQL-8.0-orange)
![Tailwind](https://img.shields.io/badge/Tailwind-CSS-38B2AC)

## 📋 Daftar Isi

- [Tentang Aplikasi](#-tentang-aplikasi)
- [Fitur Utama](#-fitur-utama)
- [Teknologi](#-teknologi)
- [Instalasi](#-instalasi)
- [Konfigurasi](#-konfigurasi)
- [Penggunaan](#-penggunaan)
- [API Documentation](#-api-documentation)
- [Deployment](#-deployment)
- [Kontribusi](#-kontribusi)

## 🎯 Tentang Aplikasi

PPID BPMP Kaltim adalah aplikasi web modern yang dirancang untuk memfasilitasi transparansi informasi publik sesuai dengan UU No. 14 Tahun 2008 tentang Keterbukaan Informasi Publik. Aplikasi ini menyediakan platform digital untuk:

- **Permohonan Informasi Publik** - Sistem pengajuan permohonan informasi yang mudah dan terstruktur
- **Formulir Keberatan** - Mekanisme pengajuan keberatan atas penolakan informasi
- **Tracking System** - Pelacakan status permohonan secara real-time
- **Dashboard Admin** - Panel administrasi untuk mengelola permohonan
- **Sistem Notifikasi** - Pemberitahuan otomatis via email dan toast notifications

## ✨ Fitur Utama

### 🌐 **Portal Publik**
- **Homepage Responsif** dengan informasi PPID
- **Formulir Permohonan Informasi** dengan validasi lengkap
- **Formulir Keberatan** untuk pengajuan banding
- **Sistem Tracking** dengan ID 6-digit yang user-friendly
- **Halaman Informasi** dengan berbagai kategori informasi publik
- **Halaman Regulasi** dengan sistem tag dan filter
- **Aksesibilitas** dengan dukungan screen reader dan kontrol aksesibilitas

### 👨‍💼 **Dashboard Admin**
- **Manajemen Permohonan** - CRUD operations untuk semua permohonan
- **Manajemen Keberatan** - Pengelolaan formulir keberatan
- **Sistem Tags** - Manajemen tag untuk kategorisasi konten
- **Manajemen Posts** - CMS untuk konten website
- **Statistik & Analytics** - Dashboard analitik dengan grafik interaktif
- **Manajemen Users** - Sistem user management
- **Upload Files** - Sistem upload file dengan validasi

### 🔐 **Keamanan & Autentikasi**
- **JWT Authentication** dengan httpOnly cookies
- **Role-based Access Control** (RBAC)
- **Rate Limiting** untuk mencegah spam
- **Input Validation** dengan Zod schema
- **CSRF Protection** dan security headers
- **Password Hashing** dengan bcrypt

### 📱 **User Experience**
- **Responsive Design** - Mobile-first approach
- **Progressive Web App** (PWA) ready
- **Dark/Light Mode** toggle
- **Toast Notifications** untuk feedback user
- **Loading States** dan error handling
- **Keyboard Navigation** support

## 🛠️ Teknologi

### **Frontend**
- **Next.js 14** - React framework dengan App Router
- **React 18** - Library UI dengan hooks modern
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Framer Motion** - Animation library
- **React Hook Form** - Form management
- **SWR** - Data fetching dengan caching
- **React Hot Toast** - Toast notifications

### **Backend**
- **Next.js API Routes** - Serverless API endpoints
- **Prisma ORM** - Database toolkit dan ORM
- **MySQL** - Relational database
- **JWT** - JSON Web Tokens untuk autentikasi
- **Bcrypt** - Password hashing
- **Zod** - Schema validation

### **Development Tools**
- **TypeScript** - Type safety (optional)
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Husky** - Git hooks
- **Vercel** - Deployment platform

### **External Services**
- **TinyMCE** - Rich text editor
- **Nodemailer** - Email sending
- **QR Code Generator** - Untuk bukti pengiriman

## 🚀 Instalasi

### **Prerequisites**
- Node.js 18+
- MySQL 8.0+
- npm atau yarn

### **Clone Repository**
```bash
git clone https://github.com/nazry/eppid_27-9.git
cd eppid_27-9
```

### **Install Dependencies**
```bash
npm install
# atau
yarn install
```

### **Setup Database**
```bash
# Setup MySQL database
mysql -u root -p
CREATE DATABASE ppiddb;

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma db push
```

### **Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file dengan konfigurasi Anda
```

### **Development Server**
```bash
npm run dev
# atau
yarn dev
```

Aplikasi akan berjalan di `http://localhost:3002`

## ⚙️ Konfigurasi

### **Environment Variables**

#### **Required (Wajib)**
```env
DATABASE_URL="mysql://username:password@localhost:3306/ppiddb"
JWT_SECRET="your-very-long-and-secure-jwt-secret-key"
NEXT_PUBLIC_TINYMCE_API_KEY="your-tinymce-api-key"
```

#### **Application Configuration**
```env
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3002
NEXT_PUBLIC_APP_NAME="PPID BPMP Prov. Kaltim"
NEXT_PUBLIC_APP_DESCRIPTION="Portal PPID BPMP Provinsi Kalimantan Timur"
```

#### **Social Media & External URLs**
```env
NEXT_PUBLIC_FACEBOOK_URL=https://fb.com/ultbpmpkaltim
NEXT_PUBLIC_INSTAGRAM_URL=https://www.instagram.com/bpmpprovkaltim
NEXT_PUBLIC_WHATSAPP_URL=https://wa.me/+6282148788787
NEXT_PUBLIC_YOUTUBE_URL=https://www.youtube.com/@BPMPProvinsiKalimantanTimur
NEXT_PUBLIC_KEMDIKBUD_URL=https://www.kemdikbud.go.id
```

#### **Production Configuration**
```env
NEXT_PUBLIC_PRODUCTION_DOMAIN=https://ppid-bpmp-kaltim.go.id
NEXT_PUBLIC_ALLOWED_IMAGE_DOMAINS=localhost,ppid-bpmp-kaltim.go.id,kemdikbud.go.id
```

#### **Email Configuration (Optional)**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

### **Validasi Konfigurasi**
```bash
# Jalankan script validasi
node validate-env-config.js
```

## 📖 Penggunaan

### **Untuk Pengguna Publik**

#### **1. Mengajukan Permohonan Informasi**
- Kunjungi `/permohonan`
- Isi formulir dengan lengkap
- Upload dokumen pendukung (opsional)
- Submit dan dapatkan ID tracking 6-digit

#### **2. Mengajukan Keberatan**
- Kunjungi `/keberatan`
- Isi formulir keberatan
- Upload dokumen pendukung
- Submit dan dapatkan ID tracking

#### **3. Melacak Status Permohonan**
- Kunjungi `/informasi`
- Masukkan ID tracking 6-digit
- Lihat status dan progress permohonan

### **Untuk Administrator**

#### **1. Login ke Dashboard**
- Kunjungi `/login`
- Masukkan username dan password
- Akses dashboard di `/dashboard`

#### **2. Mengelola Permohonan**
- Lihat daftar permohonan di `/dashboard/permohonan`
- Update status permohonan
- Download atau view dokumen
- Kirim notifikasi ke pemohon

#### **3. Mengelola Konten**
- Kelola posts di `/dashboard/posts`
- Kelola tags di `/dashboard/tags`
- Upload files di `/dashboard/upload`

## 🔌 API Documentation

### **Authentication Endpoints**
```
POST /api/auth/login     - Login user
POST /api/auth/logout    - Logout user
GET  /api/auth/me        - Get current user
```

### **Permohonan Endpoints**
```
GET    /api/permohonan           - List permohonan
POST   /api/permohonan/submit    - Submit permohonan baru
GET    /api/permohonan/[id]      - Get permohonan by ID
PATCH  /api/permohonan/[id]      - Update permohonan
DELETE /api/permohonan/[id]      - Delete permohonan
```

### **Keberatan Endpoints**
```
GET    /api/keberatan        - List keberatan
POST   /api/keberatan        - Submit keberatan baru
GET    /api/keberatan/[id]   - Get keberatan by ID
PATCH  /api/keberatan/[id]   - Update keberatan
```

### **Content Management**
```
GET    /api/posts           - List posts
POST   /api/posts           - Create post
GET    /api/tags            - List tags
POST   /api/tags            - Create tag
GET    /api/statistics      - Get statistics
```

### **File Management**
```
POST   /api/upload          - Upload file
GET    /api/files/[id]      - Download file
DELETE /api/files/[id]      - Delete file
```

### **Testing API**
```bash
# Test semua endpoints
node test-api-endpoints.js

# Test permohonan API
node test-permohonan-api.js
```
