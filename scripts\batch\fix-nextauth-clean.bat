@echo off
echo ===============================================
echo    NextAuth Error Fix - Clean Build
echo ===============================================

echo.
echo [1/3] Cleaning build cache...
if exist .next rmdir /s /q .next
echo ✅ Cache cleared

echo.
echo [2/3] Installing dependencies...
npm install
echo ✅ Dependencies installed

echo.
echo [3/3] Building application...
npm run build
echo ✅ Build completed

echo.
echo ===============================================
echo ✅ NextAuth Error Fix Applied Successfully!
echo ===============================================
echo.
echo To start server:
echo   npm run start    (production)
echo   npm run dev      (development)
echo.
echo To deploy:
echo   npm run build:standalone
echo.
pause
