@echo off
echo ===============================================
echo    Accordion Feature Test
echo ===============================================

echo.
echo [1/4] Testing Accordion Function...
echo Looking for insertAccordion function in SimpleHTMLEditor.jsx
findstr /n "insertAccordion" app\components\SimpleHTMLEditor.jsx
echo.

echo [2/4] Testing Accordion Modal...  
echo Looking for showAccordionModal state in SimpleHTMLEditor.jsx
findstr /n "showAccordionModal" app\components\SimpleHTMLEditor.jsx
echo.

echo [3/4] Testing Accordion Templates...
echo Looking for getAccordionTemplate function
findstr /n "getAccordionTemplate" app\components\SimpleHTMLEditor.jsx
echo.

echo [4/4] Testing SafeContentRenderer Support...
echo Looking for details and summary tags support
findstr /n "details\|summary" app\components\SafeContentRenderer.jsx
echo.

echo ===============================================
echo ✅ Accordion Feature Tests Complete!
echo ===============================================
echo.
echo Available Accordion Templates:
echo 📋 Info (Blue) - For general information
echo ⚠️ Warning (Yellow) - For important warnings
echo ✅ Success (Green) - For success confirmations  
echo ❓ FAQ (Purple) - For questions and answers
echo.
echo How to use:
echo 1. Open Simple Editor
echo 2. Click "📁 Insert Accordion" button
echo 3. Choose template from modal
echo 4. Preview and insert
echo 5. Edit content as needed
echo.
pause
