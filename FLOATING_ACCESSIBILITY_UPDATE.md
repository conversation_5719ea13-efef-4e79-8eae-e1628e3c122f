# Update Floating Effect untuk Tombol Aksesibilitas

## Deskripsi
Update styling untuk memberikan efek floating yang lebih menarik pada tombol aksesibilitas.

## <PERSON><PERSON><PERSON> Yang <PERSON>lakukan

### 1. <PERSON><PERSON> Utama (Main Button)
- **Ukuran**: Diperbesar dari `w-14 h-14` ke `w-16 h-16`
- **Shadow**: Ditingkatkan dari `shadow-lg` ke `shadow-2xl` 
- **Border**: Ditambahkan `border-4 border-white`
- **Custom Shadow**: Ditambahkan glow effect merah dengan `boxShadow`
- **Hover Effect**: 
  - Scale dari `1.05` ke `1.1`
  - Tambahan `hover:shadow-red-500/30`
  - <PERSON><PERSON><PERSON> `hover:scale-110`
- **Animasi Floating**: 
  - Gerakan naik-turun otomatis `y: [0, -5, 0]`
  - Durasi 3 detik dengan repeat infinite
  - Easing `easeInOut`

### 2. Icon Accessibility
- **Ukuran**: Diperbesar dari `size={22}` ke `size={24}`

### 3. Container Positioning
- **Posisi**: Di<PERSON>h dari `bottom-6 right-6` ke `bottom-8 right-8`
- Member<PERSON>n ruang lebih untuk efek floating

### 4. Menu Dropdown
- **Padding**: Ditingkatkan dari `p-4` ke `p-5`
- **Margin**: Ditingkatkan dari `mb-4` ke `mb-6`
- **Border Radius**: Diubah dari `rounded-lg` ke `rounded-2xl`
- **Shadow**: Ditingkatkan dengan custom shadow yang lebih dramatis
- **Border**: Ditambahkan `border border-gray-100`
- **Backdrop**: Ditambahkan `backdrop-blur-sm`

### 5. Close Button
- **Padding**: Ditingkatkan dari `p-1` ke `p-2`
- **Shape**: Diubah dari `rounded-md` ke `rounded-full`
- **Hover**: Ditambahkan `hover:bg-gray-100`
- **Focus**: Diubah ring color ke red untuk konsistensi
- **Transition**: Ditambahkan `transition-all duration-200`

## Efek Visual Baru

### ✨ Floating Animation
- Tombol bergerak naik-turun secara halus
- Animasi berjalan terus menerus (infinite loop)
- Tidak mengganggu interaksi user

### 🌟 Enhanced Shadow
- Shadow dengan multiple layers
- Glow effect warna merah
- Depth yang lebih dalam

### 🎯 Better Hover States
- Scale effect lebih pronounced
- Color transitions yang smooth
- Shadow yang responsif

### 📱 Improved Accessibility
- Ukuran tombol lebih besar (lebih mudah di-tap)
- Focus states yang lebih jelas
- Animasi bisa dimatikan dengan `reducedMotion`

## Responsivitas
- Semua efek tetap responsif
- Animasi otomatis mati jika user mengaktifkan `reducedMotion`
- Maintain accessibility standards

## Status
✅ Implementasi floating effect selesai
✅ Kompatibel dengan existing accessibility features
✅ Responsive dan accessible
