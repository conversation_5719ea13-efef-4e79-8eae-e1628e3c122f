'use client';

import { useState, useEffect, useRef } from 'react';

let CKEditor = null;
let ClassicEditor = null;
let HtmlEmbed = null;
let SourceEditing = null;

export default function CustomCKEditor({ 
  data = '', 
  onChange, 
  config = {},
  height = 600 
}) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const editorRef = useRef(null);

  useEffect(() => {
    setContent(data);
  }, [data]);

  useEffect(() => {
    let isMounted = true;

    const loadCKEditor = async () => {
      try {
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return;
        }

        // Load CKEditor modules only once
        if (!CKEditor || !ClassicEditor || !HtmlEmbed || !SourceEditing) {
          try {
            const [
              ckeditorReact, 
              classicEditorModule
            ] = await Promise.all([
              import('@ckeditor/ckeditor5-react'),
              import('@ckeditor/ckeditor5-build-classic')
            ]);

            CKEditor = ckeditorReact.CKEditor;
            ClassicEditor = classicEditorModule.default;

            // Try to import plugins individually
            try {
              const htmlEmbedModule = await import('@ckeditor/ckeditor5-html-embed');
              const sourceEditingModule = await import('@ckeditor/ckeditor5-source-editing');
              
              HtmlEmbed = htmlEmbedModule.HtmlEmbed;
              SourceEditing = sourceEditingModule.SourceEditing;

              // Add plugins to ClassicEditor if available
              if (HtmlEmbed && SourceEditing) {
                ClassicEditor.builtinPlugins.push(HtmlEmbed, SourceEditing);
              }
            } catch (pluginError) {
              console.warn('Could not load advanced plugins, using basic editor:', pluginError);
              // Set dummy values to prevent re-loading
              HtmlEmbed = null;
              SourceEditing = null;
            }
          } catch (coreError) {
            throw new Error(`Failed to load CKEditor core: ${coreError.message}`);
          }
        }

        if (isMounted) {
          setEditorLoaded(true);
        }

      } catch (err) {
        console.error('CKEditor loading error:', err);
        if (isMounted) {
          setError(err.message);
        }
      }
    };

    loadCKEditor();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleEditorChange = (event, editor) => {
    const data = editor.getData();
    setContent(data);
    if (onChange) {
      onChange(data);
    }
  };

  // Custom editor configuration with HTML Embed and Source Editing
  const editorConfig = {
    licenseKey: 'GPL',
    toolbar: HtmlEmbed && SourceEditing ? [
      'heading',
      '|',
      'bold',
      'italic', 
      'link',
      '|',
      'htmlEmbed',
      'sourceEditing',
      '|',
      'undo',
      'redo'
    ] : [
      // Fallback toolbar if advanced plugins not available
      'heading',
      '|',
      'bold',
      'italic', 
      'link',
      'bulletedList',
      'numberedList',
      '|',
      'undo',
      'redo'
    ],
    placeholder: HtmlEmbed ? 
      'Mulai menulis konten di sini... Gunakan HTML Embed untuk menambahkan accordion.' :
      'Mulai menulis konten di sini...',
    language: 'id',
    htmlEmbed: HtmlEmbed ? {
      showPreviews: true,
      sanitizeHtml: (inputHtml) => {
        // Allow specific HTML tags for accordion
        return inputHtml;
      }
    } : undefined,
    ...config
  };

  if (!editorLoaded) {
    return (
      <div 
        style={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          backgroundColor: '#f9fafb',
          minHeight: `${height}px`
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '32px',
            height: '32px',
            border: '2px solid #d1d5db',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 8px'
          }}></div>
          <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading Advanced Editor...</span>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '16px', border: '1px solid #fca5a5', borderRadius: '6px', backgroundColor: '#fef2f2' }}>
        <p style={{ color: '#dc2626', marginBottom: '8px', fontSize: '14px' }}>
          Failed to load Advanced Editor: {error}
        </p>
        <textarea
          value={content}
          onChange={(e) => {
            setContent(e.target.value);
            if (onChange) onChange(e.target.value);
          }}
          placeholder="Mulai menulis konten di sini..."
          style={{
            width: '100%',
            height: `${height - 50}px`,
            padding: '12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px',
            lineHeight: '1.5',
            resize: 'vertical',
            fontFamily: 'inherit'
          }}
        />
      </div>
    );
  }

  if (editorLoaded && CKEditor && ClassicEditor) {
    return (
      <div style={{ position: 'relative' }}>
        {/* Custom styles for CKEditor */}
        <style jsx global>{`
          .ck-editor__editable_inline {
            min-height: ${height - 100}px !important;
          }
          .ck-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            font-size: 14px;
            line-height: 1;
          }
          .ck-toolbar {
            border-color: #d1d5db;
            background: #f9fafb;
            border-radius: 6px 6px 0 0;
          }
          .ck-editor__editable {
            border-color: #d1d5db;
            border-radius: 0 0 6px 6px;
          }
          .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
          .ck-toolbar__separator {
            background: #d1d5db;
          }
          .ck-button:hover {
            background: #e5e7eb;
          }
          .ck-button.ck-on {
            background: #dbeafe;
            color: #1d4ed8;
          }
          
          /* Custom styling for HTML Embed button */
          .ck-button[data-cke-tooltip-text*="HTML embed"] .ck-button__label {
            font-family: monospace;
            font-weight: bold;
          }
          
          /* Styling for accordion in editor preview */
          .ck-content details {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
            background-color: #f9fafb;
          }
          
          .ck-content summary {
            cursor: pointer;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 8px;
          }
          
          .ck-content summary:hover {
            color: #1d4ed8;
          }
          
          .ck-content details[open] summary {
            margin-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 8px;
          }
          
          .ck-content details > div:not(summary) {
            margin-top: 8px;
            color: #374151;
          }
        `}</style>
        
        <CKEditor
          editor={ClassicEditor}
          data={content}
          config={editorConfig}
          onChange={handleEditorChange}
          onReady={(editor) => {
            editorRef.current = editor;
            
            // Add custom help text
            const editableElement = editor.editing.view.document.getRoot();
            console.log('Advanced CKEditor ready with HTML Embed and Source Editing');
          }}
          onError={(error) => {
            console.error('CKEditor error:', error);
            setError(error.message);
          }}
        />
        
        {/* Help text for accordion */}
        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-800 mb-2">
            {HtmlEmbed ? '💡 Cara Membuat Accordion:' : 'ℹ️ Info Editor:'}
          </h4>
          <div className="text-xs text-blue-700 space-y-1">
            {HtmlEmbed ? (
              <>
                <p>1. Klik tombol <strong>&lt;/&gt;</strong> (HTML Embed) di toolbar</p>
                <p>2. Masukkan kode HTML accordion berikut:</p>
                <code className="block bg-blue-100 p-2 rounded mt-1 text-xs">
{`<details class="border rounded-md p-4 my-3">
  <summary class="cursor-pointer font-semibold text-blue-600">Klik untuk buka konten</summary>
  <div class="mt-2 text-gray-700">
    Ini isi accordion yang bisa dikustomisasi.
  </div>
</details>`}
                </code>
                <p>3. Klik "Save" untuk menyisipkan accordion ke konten</p>
              </>
            ) : (
              <p>Editor dengan fitur dasar. Plugin HTML Embed tidak tersedia.</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return null;
}
