import { PrismaClient } from '@prisma/client';

// Fungsi untuk membuat PrismaClient dengan konfigurasi yang tepat
function createPrismaClient() {
  return new PrismaClient({
    log: ['error', 'warn'], // stop logging SQL queries in console
    errorFormat: process.env.NODE_ENV === 'development' ? 'pretty' : 'minimal',
  });
}

// Singleton pattern untuk development
let prisma;

if (process.env.NODE_ENV === 'production') {
  prisma = createPrismaClient();
} else {
  // Gunakan global object untuk development hot reload
  if (!global.__prisma) {
    global.__prisma = createPrismaClient();
  }
  prisma = global.__prisma;
}

export { prisma };
