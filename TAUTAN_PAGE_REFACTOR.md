# Refactor Halaman Tautan untuk Konsistensi dengan Halaman Profil

## Deskripsi
Refactoring styling halaman tautan (`/tautan`) untuk konsistensi dengan halaman profil (`/profil`) dan penambahan card baru "Rumah <PERSON>di<PERSON>".

## <PERSON><PERSON><PERSON>

### 1. Struktur dan Layout
- **Background**: <PERSON><PERSON><PERSON> da<PERSON> `from-blue-100 to-purple-100` menjadi `from-blue-50 to-indigo-200` (sesuai profil)
- **Container**: Menggunakan struktur container yang sama dengan halaman profil
- **Grid**: Tetap menggunakan responsive grid `grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`
- **Layout**: Menerapkan structure `min-h-screen` dengan centered content

### 2. Header/Title
- **Typography**: Menggunakan `text-5xl font-bold text-blue-900` (sesuai profil)
- **Animation**: Menerapkan same fade-in animation dengan `initial={{ opacity: 0, y: -20 }}`
- **Spacing**: <PERSON><PERSON>ten margin bottom `mb-8`

### 3. Card Styling
#### Sebelum (Old Style):
```jsx
// Simple card dengan background solid
className={`flex flex-col items-center justify-center h-64 p-6 rounded-lg shadow-md cursor-pointer
            ${isHovered ? 'bg-blue-500 text-white' : 'bg-white text-gray-800'}`}
```

#### Setelah (New Style - sesuai profil):
```jsx
// Gradient cards dengan ring effect
className={`bg-gradient-to-r ${link.color} p-6 rounded-lg shadow-md transition-all duration-300 ease-in-out
            ${hoveredIndex === index ? 'ring-2 ring-blue-400 shadow-lg shadow-blue-200/50' : ''}
            ${hoveredIndex !== null && hoveredIndex !== index ? 'opacity-50 blur-sm' : ''}`}
```

### 4. Color Scheme
Menggunakan gradient color scheme yang sama dengan halaman profil:
- **Card 1 (Portal Data)**: `from-blue-50 to-blue-100` dengan `text-blue-600/700/800`
- **Card 2 (Dapodik)**: `from-sky-50 to-sky-100` dengan `text-sky-600/700/800`
- **Card 3 (Sekolah Kita)**: `from-indigo-50 to-indigo-100` dengan `text-indigo-600/700/800`
- **Card 4 (Rumah Pendidikan)**: `from-cyan-50 to-cyan-100` dengan `text-cyan-600/700/800`

### 5. Icon dan Content Layout
- **Icon positioning**: Centered di atas dengan margin bottom
- **Icon size**: Konsisten `w-8 h-8` (sesuai profil)
- **Title**: Typography `text-xl font-semibold` dengan color matching
- **Description**: Ditambahkan deskripsi detail untuk setiap card

### 6. Animation dan Interactions
- **Hover effects**: Ring dan shadow yang sama dengan profil
- **Blur effect**: Background cards blur saat hover pada card lain
- **Scale animation**: `whileHover={{ scale: 1.05 }}` dan `whileTap={{ scale: 0.95 }}`
- **Stagger animation**: Delay `index * 0.1` untuk smooth entrance

## Card Baru yang Ditambahkan

### Rumah Pendidikan
```jsx
{
  title: 'Rumah Pendidikan',
  icon: HomeIcon,
  link: 'https://rumah.pendidikan.go.id/',
  color: "from-cyan-50 to-cyan-100",
  description: "Platform digital yang menyediakan berbagai layanan dan informasi pendidikan terpadu."
}
```

## Card Yang Sudah Ada (Updated)

### 1. Portal Data
- **Link**: `https://data.kemdikbud.go.id/`
- **Deskripsi**: "Portal data terpusat Kementerian Pendidikan dan Kebudayaan untuk mengakses berbagai informasi statistik pendidikan."
- **Color**: Blue gradient

### 2. Dapodik
- **Link**: `https://dapo.kemdikbud.go.id/`
- **Deskripsi**: "Data Pokok Pendidikan untuk mengakses informasi sekolah, siswa, guru, dan tenaga kependidikan."
- **Color**: Sky gradient

### 3. Sekolah Kita
- **Link**: `https://sekolah.data.kemdikbud.go.id/`
- **Deskripsi**: "Platform informasi sekolah yang menyajikan data profil sekolah di seluruh Indonesia."
- **Color**: Indigo gradient

## Konsistensi dengan Halaman Profil

### ✅ Elemen yang Sekarang Konsisten:
1. **Background gradient**: Same blue color scheme
2. **Typography**: Same font sizes dan weights
3. **Card layout**: Same padding, border radius, shadow
4. **Animation timing**: Same duration dan delays
5. **Hover effects**: Same ring dan blur effects
6. **Color palette**: Same blue/sky/indigo/cyan progression
7. **Grid responsiveness**: Same breakpoints
8. **Overall structure**: Same container dan spacing

### 🎨 Visual Improvements:
- Lebih professional dan cohesive
- Better readability dengan descriptions
- Consistent brand identity
- Modern glassmorphism effects
- Smooth interactions

## Status
✅ **Refactoring selesai** - Halaman tautan sekarang konsisten dengan halaman profil
✅ **Card baru ditambahkan** - "Rumah Pendidikan" berhasil diintegrasikan
✅ **No errors** - Semua komponen berfungsi dengan baik
✅ **Responsive** - Layout tetap responsive di semua device sizes
