"use client"

import { useState, useEffect, Fragment } from 'react'
import { Disclosure, Transition } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import Image from "next/image" 
import Link from 'next/link'
import { usePathname } from 'next/navigation'

const navigation = [
    { name: '<PERSON><PERSON><PERSON>', href: '/', current: true },
    { name: 'Profil', href: '/profil', current: false },
    { name: 'Informasi Publik', href: '/regulasi', current: false },
    { name: '<PERSON><PERSON><PERSON> Informasi', href: '/informasi', current: false },
    { name: '<PERSON><PERSON>', href: '/tautan', current: false },
]

function classNames(...classes) {
    return classes.filter(Boolean).join(' ')
}

export default function Navbar() {
    const [isScrolled, setIsScrolled] = useState(false)
    const pathname = usePathname()

    // Update current status berdasarkan path saat ini
    const navigationWithCurrentStatus = navigation.map(item => ({
        ...item,
        current: pathname === item.href
    }))

    useEffect(() => {
        // Implementasi throttling untuk meningkatkan performa scroll
        let lastScrollTime = 0;
        const throttleTime = 100; // ms
        
        const handleScroll = () => {
            const now = Date.now();
            if (now - lastScrollTime >= throttleTime) {
                lastScrollTime = now;
                if (window.scrollY > 50) {
                    setIsScrolled(true)
                } else {
                    setIsScrolled(false)
                }
            }
        }

        window.addEventListener('scroll', handleScroll, { passive: true })
        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

    return (
        <Disclosure
            as="nav"
            className={
                // Always use a semi-opaque white background to ensure contrast
                "sticky top-0 z-50 bg-white/90 shadow-md backdrop-blur-md transition-all duration-300 ease-in-out"
            }
            aria-label="Navigasi Utama"
        >
            {({ open }) => (
                <>
                    <div className="px-2 mx-auto max-w-7xl sm:px-6 lg:px-8">
                        <div className="relative flex items-center justify-between h-16">
                            {/* Mobile menu button */}
                            <div className="absolute inset-y-0 left-0 z-10 flex items-center sm:hidden">
                                <Disclosure.Button 
                                    className={
                                        classNames(
                                            'inline-flex items-center justify-center p-2 transition-colors rounded-md focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500',
                                            // Always dark icon for readability
                                            'text-gray-900 hover:text-white hover:bg-primary-600'
                                        )
                                    }
                                    aria-label={open ? "Tutup menu utama" : "Buka menu utama"}
                                    aria-expanded={open}
                                    aria-controls="mobile-menu"
                                >
                                    <span className="sr-only">{open ? "Tutup menu" : "Buka menu"}</span>
                                    {open ? (
                                        <XMarkIcon className="block w-6 h-6" aria-hidden="true" />
                                    ) : (
                                        <Bars3Icon className="block w-6 h-6" aria-hidden="true" />
                                    )}
                                </Disclosure.Button>
                            </div>

                            {/* Nama brand di kanan untuk layar kecil */}
                            <div className="absolute inset-y-0 right-0 z-10 flex items-center pr-4 sm:hidden">
                                <Link href="/" className={classNames('text-lg font-semibold', 'text-gray-900')}>PPID</Link>
                            </div>

                            {/* Logo dan Nama brand di sebelah kanan logo pada layar besar */}
                            <div className="flex items-center justify-center flex-1 sm:justify-start">
                                <div className="flex items-center space-x-2">
                                    <Link href="/" className="flex items-center rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" aria-label="Beranda PPID BPMP Provinsi Kaltim">
                                        <Image
                                            src="/tutwuri.png"
                                            alt="Logo Tutwuri Handayani BPMP"
                                            width={32}
                                            height={32}
                                            className="w-8 h-8"
                                            priority
                                        />
                                        <span className={classNames('hidden ml-2 text-lg font-semibold sm:inline-block', 'text-gray-900')}>
                                            PPID BPMP Prov. Kaltim
                                        </span>
                                    </Link>
                                </div>
                            </div>

                            {/* Menu di tengah untuk layar besar */}
                            <div className="hidden sm:flex sm:ml-6 sm:space-x-4" role="navigation">
                                {navigationWithCurrentStatus.map((item) => (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        className={classNames(
                                            item.current
                                                ? 'bg-primary-700 text-white'
                                                : 'text-gray-900 hover:bg-primary-600 hover:text-white',
                                            'px-3 py-2 rounded-md text-sm font-medium transition-colors'
                                        )}
                                        aria-current={item.current ? 'page' : undefined}
                                    >
                                        {item.name}
                                    </Link>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Menu Mobile dengan animasi */}
                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Disclosure.Panel className="sm:hidden" id="mobile-menu" role="navigation">
                            <div className="px-2 pt-2 pb-3 space-y-1 bg-white rounded-b-lg shadow-lg">
                                {navigationWithCurrentStatus.map((item) => (
                                    <Disclosure.Button
                                        key={item.name}
                                        as="a"
                                        href={item.href}
                                        className={classNames(
                                            item.current
                                                ? 'bg-primary-700 text-white'
                                                : 'text-gray-900 hover:bg-primary-600 hover:text-white',
                                            'block px-3 py-2 rounded-md text-base font-medium transition-colors'
                                        )}
                                        aria-current={item.current ? 'page' : undefined}
                                    >
                                        {item.name}
                                    </Disclosure.Button>
                                ))}
                            </div>
                        </Disclosure.Panel>
                    </Transition>
                </>
            )}
        </Disclosure>
    )
}
