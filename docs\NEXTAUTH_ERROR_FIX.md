# NextAuth Error Fix

## Problem
Error terjadi pada production server:
```
[next-auth][error][NO_SECRET]
https://next-auth.js.org/errors#no_secret
Please define a `secret` in production.
Error [MissingSecretError]: Please define a `secret` in production.
```

## Root Cause
Aplikasi **TIDAK menggunakan NextAuth** tetapi error masih muncul karena:
1. Cache build yang lama
2. Leftover dependencies atau imports
3. Environment variables yang salah

## Solution

### 1. Clean Build
```bash
# Hapus cache build
rmdir /s /q .next

# Install ulang dependencies
npm install

# Build ulang
npm run build
```

### 2. Remove NextAuth Environment Variables
Pastikan file `.env` **TIDAK** mengandung:
```bash
# HAPUS jika ada:
# NEXTAUTH_SECRET=...
# NEXTAUTH_URL=...
```

### 3. Verify No NextAuth Dependencies
Check `package.json` tidak ada:
```json
// Pastikan TIDAK ada:
// "next-auth": "...",
// "@next-auth/...": "...",
```

### 4. Custom JWT Authentication
Aplikasi ini menggunakan **custom JWT**, bukan NextAuth:

#### Environment Variables yang Diperlukan:
```bash
# JWT Configuration (BUKAN NextAuth)
JWT_SECRET=your-very-long-and-secure-jwt-secret-key-here
JWT_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-key-here
```

#### Authentication Endpoints:
- `POST /api/auth/login` - Custom login
- `POST /api/auth/logout` - Custom logout  
- `GET /api/auth/me` - Get user info
- `POST /api/auth/refresh` - Refresh token

### 5. PM2 Restart (if using PM2)
```bash
# Restart aplikasi
pm2 restart ppid-bpmp

# Atau stop dan start ulang
pm2 stop ppid-bpmp
pm2 start ecosystem.config.json
```

## Verification
Setelah fix, pastikan:
- ✅ Tidak ada error `[next-auth][error]` di logs
- ✅ Login menggunakan `/api/auth/login` berhasil
- ✅ Authentication menggunakan JWT token
- ✅ No NextAuth references di code

## Files Modified
- Clean build cache (`.next/`)
- Environment configuration check
- `docs/NEXTAUTH_ERROR_FIX.md` - This documentation

---

*Fixed: August 7, 2025*
