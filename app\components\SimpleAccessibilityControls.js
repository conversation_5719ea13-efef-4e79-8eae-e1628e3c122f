'use client';

export default function SimpleAccessibilityControls() {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button 
        className="bg-red-600 text-white p-3 rounded-full shadow-lg hover:bg-red-700 transition-colors"
        aria-label="Kontrol Aksesibilitas untuk Penyandang Disabilitas"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.5c3.5 0 6.375 1.5 6.375 3.375S15.5 11.25 12 11.25s-6.375-1.5-6.375-3.375S8.5 4.5 12 4.5zm0 0v15m-7.5-6h15" />
          <circle cx="12" cy="7.875" r="1.125" strokeWidth={2} />
        </svg>
      </button>
    </div>
  );
}
