'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import PostEditor from '../../../../components/PostEditor';
import { useContentValidation } from '../../../../hooks/useContentValidation';

export default function EditPostPage(props) {
  const params = use(props.params);
  const router = useRouter();
  const { id } = params;

  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [slug, setSlug] = useState('');
  const [published, setPublished] = useState(false);
  const [availableTags, setAvailableTags] = useState([]);
  const [selectedTags, setSelectedTags] = useState('');
  const [isContentLoaded, setIsContentLoaded] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  
  // Use content validation hook
  const { validateAll } = useContentValidation();

  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch post data
        const postResponse = await fetch(`/api/posts/${id}`);
        if (!postResponse.ok) {
          throw new Error('Failed to fetch post');
        }
        
        const postData = await postResponse.json();
        setPost(postData);
        
        // Populate form fields
        setTitle(postData.title || '');
        setContent(postData.content || '');
        setExcerpt(postData.excerpt || '');
        setSlug(postData.slug || '');
        setPublished(postData.published || false);
        
        // Mark content as loaded
        setIsContentLoaded(true);
        
        // Fetch available tags first
        const tagsResponse = await fetch('/api/tags');
        if (!tagsResponse.ok) {
          throw new Error('Failed to fetch tags');
        }
        
        const tagsData = await tagsResponse.json();
        setAvailableTags(tagsData.tags || []);
        
        // Set selected tags after tags are loaded (pakai relasi tagsonposts)
        if (Array.isArray(postData.tagsonposts) && postData.tagsonposts.length > 0) {
          const first = postData.tagsonposts[0];
          const tagId = first?.tagId || first?.tag?.id;
          setSelectedTags(tagId ? String(tagId) : '');
        } else {
          setSelectedTags('');
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [id]);
  const handleEditorChange = (newContent) => {
    setContent(newContent);
  };

  const handleTagChange = (e) => {
    setSelectedTags(e.target.value);
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .trim();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form data
    const formData = { title, content, excerpt, slug };
    const { errors, isValid } = validateAll(formData);
    setValidationErrors(errors);
    
    if (!isValid) {
      toast.error('Silakan perbaiki kesalahan pada form');
      return;
    }
    
    setLoading(true);
    
    try {
      const response = await fetch(`/api/posts/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },        body: JSON.stringify({
          title,
          content,
          excerpt,
          slug,
          published,
          tags: selectedTags ? [selectedTags] : [],
          publishedAt: published ? (post.publishedAt || new Date().toISOString()) : null
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to update post');
      }
      
      toast.success(`Postingan "${title}" berhasil diperbarui!`);
      router.push('/dashboard/posts');
    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('Gagal memperbarui postingan: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !post) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 rounded-full border-t-primary-600 animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-700 bg-red-100 rounded-md">{error}</div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Edit Postingan</h1>
        <Link
          href="/dashboard/posts"
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
        >
          Kembali
        </Link>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="title" className="block mb-2 text-sm font-medium text-gray-700">
                Judul
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  if (slug === generateSlug(title)) {
                    setSlug(generateSlug(e.target.value));
                  }
                }}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  validationErrors.title ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.title && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.title}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="slug" className="block mb-2 text-sm font-medium text-gray-700">
                Slug
              </label>
              <input
                type="text"
                id="slug"
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  validationErrors.slug ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.slug && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.slug}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="excerpt" className="block mb-2 text-sm font-medium text-gray-700">
                Ringkasan
              </label>
              <textarea
                id="excerpt"
                value={excerpt}
                onChange={(e) => setExcerpt(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  validationErrors.excerpt ? 'border-red-300' : 'border-gray-300'
                }`}
                rows="3"
              ></textarea>
              {validationErrors.excerpt && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.excerpt}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="tags" className="block mb-2 text-sm font-medium text-gray-700">
                Tag
              </label>
              <select
                id="tags"
                name="tags"
                value={selectedTags}
                onChange={handleTagChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Pilih Tag</option>
                {Array.isArray(availableTags) && availableTags.length > 0 ? (
                  availableTags.map(tag => (
                    <option key={tag.id} value={tag.id.toString()}>
                      {tag.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>Tidak ada tag tersedia</option>
                )}
              </select>
              {selectedTags && (
                <p className="mt-1 text-xs text-gray-500">
                  Tag terpilih: {availableTags.find(tag => tag.id.toString() === selectedTags)?.name}
                </p>
              )}
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="published"
                checked={published}
                onChange={(e) => setPublished(e.target.checked)}
                className="w-4 h-4 border-gray-300 rounded text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="published" className="ml-2 text-sm text-gray-700">
                Publikasikan
              </label>
            </div>
          </div>
            <div>
            <label htmlFor="content" className="block mb-2 text-sm font-medium text-gray-700">
              Konten
            </label>
            {isContentLoaded ? (
              <PostEditor
                content={content}
                onChange={handleEditorChange}
                hasError={!!validationErrors.content}
                errorMessage={validationErrors.content}
                placeholder="Mulai menulis konten postingan..."
                editorType="simple"
              />
            ) : (
              <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50 min-h-[600px]">
                <div className="w-6 h-6 mr-2 border-2 border-t-2 border-gray-500 rounded-full border-t-primary-600 animate-spin"></div>
                <span className="text-sm text-gray-500">Loading content...</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {loading ? 'Menyimpan...' : 'Simpan Perubahan'}
          </button>
        </div>
      </form>
    </div>
  );
}
