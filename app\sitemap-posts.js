import { prisma } from '../../lib/prisma';
import { getProductionUrl } from '../../lib/config';

export default async function sitemap() {
  const baseUrl = getProductionUrl();

  try {
    // Get all published posts
    const posts = await prisma.post.findMany({
      where: { published: true },
      select: {
        slug: true,
        title: true,
        updatedAt: true,
        createdAt: true,
        tagsonposts: {
          select: {
            tag: {
              select: {
                name: true,
                slug: true,
              }
            }
          }
        }
      },
      orderBy: { updatedAt: 'desc' },
    });

    // Generate post pages
    const postPages = posts.map((post) => {
      // Determine priority based on recency and content
      const daysSinceUpdate = Math.floor((new Date() - new Date(post.updatedAt)) / (1000 * 60 * 60 * 24));
      let priority = 0.8;
      
      if (daysSinceUpdate <= 7) {
        priority = 0.9; // Recent posts get higher priority
      } else if (daysSinceUpdate <= 30) {
        priority = 0.8;
      } else if (daysSinceUpdate <= 90) {
        priority = 0.7;
      } else {
        priority = 0.6;
      }

      // Determine change frequency based on update patterns
      let changeFrequency = 'weekly';
      if (daysSinceUpdate <= 7) {
        changeFrequency = 'daily';
      } else if (daysSinceUpdate <= 30) {
        changeFrequency = 'weekly';
      } else {
        changeFrequency = 'monthly';
      }

      return {
        url: `${baseUrl}/posts/${post.slug}`,
        lastModified: post.updatedAt,
        changeFrequency: changeFrequency,
        priority: priority,
      };
    });

    return postPages;
  } catch (error) {
    console.error('Error generating posts sitemap:', error);
    return [];
  }
}
