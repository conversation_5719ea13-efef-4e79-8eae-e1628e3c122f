# 🚀 PPID BPMP - Deployment Guide

Panduan lengkap untuk men-deploy aplikasi PPID BPMP dalam mode standalone.

## 📋 Prerequisites

- Node.js 18+ 
- MySQL 8.0+
- Domain dan SSL Certificate (untuk production)
- Server dengan minimal 2GB RAM

## 🛠️ Metode Deployment

### 1. **Standalone Deployment (Recommended)**

#### A. Build Aplikasi
```bash
# Jalankan script build
.\build-standalone.bat

# Atau manual:
npm ci
npx prisma generate
npm run build
```

#### B. Persiapan Production Package
```bash
# Jalankan script deploy
.\deploy-production.bat
```

Script ini akan membuat folder `deploy` dengan struktur:
```
deploy/
├── server.js (Next.js standalone server)
├── .next/ (Built application)
├── public/ (Static assets)
├── .env.production (Template environment)
├── start.bat (Start script)
└── ecosystem.config.js (PM2 config)
```

#### C. Deploy ke Server
1. **Upload folder `deploy` ke server**
2. **Edit `.env.production` dengan nilai sebenarnya**
3. **Install Node.js di server**
4. **Jalankan aplikasi:**
   ```bash
   # Method 1: Direct
   node server.js
   
   # Method 2: Dengan PM2 (Recommended)
   npm install -g pm2
   pm2 start ecosystem.config.js
   ```

### 2. **Docker Deployment**

#### A. Build Docker Image
```bash
docker build -t ppid-bpmp .
```

#### B. Run dengan Docker Compose
```bash
# Copy template environment
cp .env.production.template .env.production

# Edit .env.production dengan nilai yang sesuai

# Start services
docker-compose up -d
```

#### C. Services yang berjalan:
- **App**: Next.js application (Port 3000)
- **Database**: MySQL 8.0 (Port 3306)
- **Nginx**: Reverse proxy & SSL (Port 80/443)

## ⚙️ Konfigurasi Environment

### Required Variables:
```env
DATABASE_URL=mysql://user:pass@host:port/database
JWT_SECRET=your-super-secure-secret
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### Database Setup:
```sql
-- Buat database
CREATE DATABASE ppid_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Buat user
CREATE USER 'ppid_user'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON ppid_db.* TO 'ppid_user'@'%';
FLUSH PRIVILEGES;
```

### Prisma Migration:
```bash
# Di server, jalankan migration
npx prisma migrate deploy
npx prisma db seed
```

## 🔒 Security Checklist

- [ ] Gunakan HTTPS dengan SSL certificate valid
- [ ] Set strong password untuk database
- [ ] Konfigurasi firewall (hanya port 80, 443, 22)
- [ ] Set `COOKIE_SECURE=true` di production
- [ ] Gunakan strong JWT_SECRET (min 32 karakter)
- [ ] Enable rate limiting di Nginx
- [ ] Regular backup database
- [ ] Monitor aplikasi dengan PM2/Docker logs

## 📊 Monitoring

### PM2 Commands:
```bash
pm2 status                # Cek status
pm2 logs ppid-bpmp        # Lihat logs
pm2 restart ppid-bpmp     # Restart app
pm2 reload ppid-bpmp      # Zero-downtime reload
pm2 monit                 # Real-time monitoring
```

### Docker Commands:
```bash
docker-compose logs -f app    # Lihat logs aplikasi
docker-compose ps             # Status containers
docker-compose restart app    # Restart aplikasi
```

## 🔧 Troubleshooting

### Common Issues:

1. **Database Connection Error**
   - Cek DATABASE_URL format
   - Pastikan MySQL service running
   - Cek firewall rules

2. **Permission Denied**
   - Cek file permissions (755 untuk direktori, 644 untuk file)
   - Pastikan user nextjs memiliki akses ke file

3. **Port Already in Use**
   - Cek process yang menggunakan port: `netstat -tulpn | grep :3000`
   - Kill process atau ganti port

4. **SSL Certificate Issues**
   - Pastikan certificate path benar di nginx.conf
   - Cek expiry date certificate
   - Restart nginx setelah update certificate

## 📈 Performance Tips

1. **Enable Gzip** (sudah dikonfigurasi di nginx.conf)
2. **Static File Caching** (sudah dikonfigurasi)
3. **Database Connection Pooling** (built-in Prisma)
4. **PM2 Cluster Mode** untuk multiple instances:
   ```javascript
   // ecosystem.config.js
   instances: 'max', // Gunakan semua CPU cores
   exec_mode: 'cluster'
   ```

## 📞 Support

Jika mengalami kendala deployment:

1. Cek logs aplikasi
2. Cek documentation Next.js standalone
3. Cek Prisma deployment guide
4. Contact system administrator

---

**⚠️ Important Notes:**
- Selalu backup database sebelum deployment
- Test deployment di staging environment dulu
- Monitor aplikasi setelah deployment
- Update dependencies secara berkala
