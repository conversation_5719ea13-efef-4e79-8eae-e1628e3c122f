# 📋 Panduan Edit Accordion dengan Aman

## ⚠️ **PENTING**: Cara Mengedit Konten Accordion

### 🎯 **Masalah yang <PERSON>tem<PERSON>n**
Ketika konten accordion seperti ini:
```html
<details class="p-4 my-3 border border-blue-200 rounded-md bg-blue-50">
  <summary class="mb-2 font-semibold text-blue-600 cursor-pointer">
    📋 Informasi
  </summary>
  <div class="mt-2 text-gray-700">
    <p>Masukkan informasi penting di sini...</p>
  </div>
</details>
```

Diedit di **Visual Mode**, accordion akan berubah menjadi:
```html
<p>📋 Informasi</p>
<p>Masukkan informasi penting di sini...</p>
```

## ✅ **SOLUSI YANG SUDAH DIIMPLEMENTASI**

### 1. **Auto-Switch ke Source Mode**
- Ketika konten mengandung accordion, editor otomatis beralih ke **Source Mode**
- User akan bekerja langsung dengan HTML source code

### 2. **Warning System**
- Jika user beralih ke Visual Mode saat ada accordion, muncul warning:
  ```
  ⚠️ Perhatian: Konten Accordion Terdeteksi
  Visual mode dapat mengubah format accordion. 
  Gunakan Source mode untuk mengedit konten accordion dengan aman.
  ```

### 3. **Real-time Detection**
- System mendeteksi jika accordion hilang saat editing di Visual Mode
- Warning otomatis muncul untuk memberitahu user

## 📋 **PANDUAN PENGGUNAAN**

### ✅ **Cara BENAR Mengedit Accordion:**

1. **Gunakan Source Mode** 📝
   - Klik tab "📝 Source" di editor
   - Edit HTML accordion secara langsung
   - Format accordion akan tetap terjaga

2. **Cara Edit di Source Mode:**
   ```html
   <details class="p-4 my-3 border border-blue-200 rounded-md bg-blue-50">
     <summary class="mb-2 font-semibold text-blue-600 cursor-pointer">
       📋 [EDIT JUDUL DISINI]
     </summary>
     <div class="mt-2 text-gray-700">
       <p>[EDIT KONTEN DISINI]</p>
       <p>Tambah paragraf baru jika perlu</p>
     </div>
   </details>
   ```

3. **Simpan dan Lihat Hasil:**
   - Accordion akan tampil sebagai interactive element
   - Click to expand/collapse akan berfungsi

### ❌ **HINDARI:**
- **Jangan edit accordion di Visual Mode** - akan merusak format
- **Jangan hapus tag `<details>` atau `<summary>`** - accordion tidak akan berfungsi
- **Jangan hapus class CSS** - styling akan hilang

## 🔧 **Fitur Proteksi yang Aktif**

### 1. **Auto-Detection**
- System otomatis mendeteksi konten accordion
- Editor beralih ke Source Mode secara otomatis

### 2. **Visual Warning**
- Warning muncul jika user mencoba edit di Visual Mode
- Durasi warning: 8 detik

### 3. **Real-time Monitoring**
- Deteksi perubahan yang merusak accordion
- Alert langsung jika accordion hilang

## 🎯 **Best Practices**

### ✅ **DO (Lakukan):**
1. **Selalu gunakan Source Mode** untuk edit accordion
2. **Backup konten** sebelum edit besar-besaran
3. **Test accordion** di preview setelah edit
4. **Gunakan template accordion** untuk consistency

### ❌ **DON'T (Jangan):**
1. **Jangan edit accordion di Visual Mode**
2. **Jangan hapus structure HTML** accordion
3. **Jangan abaikan warning** yang muncul
4. **Jangan edit class CSS** tanpa pengetahuan

## 🚀 **Workflow yang Disarankan**

```
1. Buka Editor → 2. Cek Mode (Source/Visual) → 3. Edit Content
                                 ↓
4. Save Changes ← 3. Preview Result ← Source Mode (untuk accordion)
```

### **Untuk Konten Biasa:** Visual Mode ✅
### **Untuk Konten Accordion:** Source Mode ✅

---

## 📞 **Troubleshooting**

### Q: Accordion saya hilang setelah edit?
**A:** Kemungkinan diedit di Visual Mode. Gunakan Source Mode dan restore HTML accordion.

### Q: Warning terus muncul?
**A:** Normal jika konten mengandung accordion. Gunakan Source Mode untuk edit.

### Q: Bagaimana cara menambah accordion baru?
**A:** Gunakan tombol "Insert Accordion" di Visual Mode, atau copy template ke Source Mode.

---

*Update: 8 Agustus 2025 - Sistem proteksi accordion aktif*
