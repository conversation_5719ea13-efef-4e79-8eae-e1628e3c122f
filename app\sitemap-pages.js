import { prisma } from '../../lib/prisma';
import { getProductionUrl } from '../../lib/config';

export default async function sitemap() {
  const baseUrl = getProductionUrl();

  try {
    // Static pages with detailed SEO optimization
    const staticPages = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${baseUrl}/informasi`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/posts`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${baseUrl}/permohonan`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${baseUrl}/keberatan`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${baseUrl}/regulasi`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
      },
      {
        url: `${baseUrl}/profil`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.6,
      },
      {
        url: `${baseUrl}/tautan`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      },
      {
        url: `${baseUrl}/status`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.4,
      },
      {
        url: `${baseUrl}/tracking`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.4,
      },
    ];

    // Get active tags (tags with published posts)
    const tags = await prisma.tag.findMany({
      where: {
        tagsonposts: {
          some: {
            post: {
              published: true,
            },
          },
        },
      },
      select: {
        slug: true,
        name: true,
        updatedAt: true,
        _count: {
          select: {
            tagsonposts: {
              where: { post: { published: true } },
            },
          },
        },
      },
    });

    const tagPages = tags.map((tag) => ({
      url: `${baseUrl}/posts/tag/${tag.slug}`,
      lastModified: tag.updatedAt,
      changeFrequency: 'weekly',
      priority: tag._count.tagsonposts > 5 ? 0.6 : 0.5,
    }));

    return [...staticPages, ...tagPages];
  } catch (error) {
    console.error('Error generating pages sitemap:', error);
    // Return static pages only if database query fails
    const staticPages = [
      {
        url: getProductionUrl(),
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${getProductionUrl()}/informasi`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${getProductionUrl()}/posts`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
    ];
    return staticPages;
  }
}
