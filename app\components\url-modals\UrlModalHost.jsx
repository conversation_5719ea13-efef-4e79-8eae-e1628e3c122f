"use client";
import { useEffect, useState, useMemo, useCallback } from "react";
import { useSearchParams, usePathname, useRouter } from "next/navigation";

// Generic URL-driven modal host
// Usage:
// - Place <UrlModalHost registry={registry} /> once in a layout or ClientWrapper
// - To open: append ?modal=<key>&modalData=<json-encoded-string> to any URL
// - Provide registry mapping keys to async components to lazily load modal UIs
//
// Example registry (in ClientWrapper):
// const registry = {
//   info: () => import("./modals/InfoModal"),
//   contact: () => import("./modals/ContactModal"),
// };
//
// Then open: /page?modal=info&modalData=%7B%22title%22%3A%22Halo%22%7D

export default function UrlModalHost({ registry, param = "modal", dataParam = "modalData" }) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const modalKey = searchParams.get(param);
  const dataStr = searchParams.get(dataParam);

  const [isOpen, setIsOpen] = useState(false);
  const [ModalComp, setModalComp] = useState(null);
  const [modalProps, setModalProps] = useState({});

  const safeData = useMemo(() => {
    if (!dataStr) return {};
    try {
      // Allow either JSON or base64 JSON
      const decoded = decodeURIComponent(dataStr);
      try {
        return JSON.parse(decoded);
      } catch {
        const b = typeof atob !== "undefined" ? atob(decoded) : decoded;
        return JSON.parse(b);
      }
    } catch {
      return {};
    }
  }, [dataStr]);

  const close = useCallback(() => {
    // Remove the modal query params while preserving others
    const sp = new URLSearchParams(Array.from(searchParams.entries()))
    sp.delete(param);
    sp.delete(dataParam);
    const rest = sp.toString();
    const url = rest ? `${pathname}?${rest}` : pathname;
    router.replace(url, { scroll: false });
    setIsOpen(false);
  }, [searchParams, pathname, router, param, dataParam]);

  useEffect(() => {
    let active = true;
    async function load() {
      if (!modalKey) {
        setIsOpen(false);
        setModalComp(null);
        return;
      }
      const loader = registry?.[modalKey];
      if (!loader) return;
      try {
        const mod = await loader();
        if (!active) return;
        const Comp = mod.default || mod.Modal || Object.values(mod)[0];
        setModalComp(() => Comp);
        setModalProps(safeData || {});
        setIsOpen(true);
      } catch (e) {
        console.error("Failed to load modal", modalKey, e);
      }
    }
    load();
    return () => { active = false; };
  }, [modalKey, registry, safeData]);

  if (!isOpen || !ModalComp) return null;

  return (
    <div aria-live="assertive">
      <ModalComp {...modalProps} onClose={close} />
    </div>
  );
}
