// components/Footer.js
"use client";
import React from 'react';
import Image from 'next/image';
import { FaFacebook, FaInstagram, FaWhatsapp, FaYoutube } from 'react-icons/fa';
import { config } from '../../lib/config';

const Footer = () => {
  const socialLinks = [
    { icon: FaFacebook, href: config.socialMedia.facebook, label: "Facebook BPMP Kaltim", color: "hover:bg-blue-600" },
    { icon: FaInstagram, href: config.socialMedia.instagram, label: "Instagram BPMP Kaltim", color: "hover:bg-pink-600" },
    { icon: FaWhatsapp, href: config.socialMedia.whatsapp, label: "WhatsApp BPMP Kaltim", color: "hover:bg-green-600" },
    { icon: FaYoutube, href: config.socialMedia.youtube, label: "YouTube BPMP Kaltim", color: "hover:bg-red-600" },
  ];

  const navigationLinks = [
    { href: "/profil", label: "Profil" },
    { href: "/informasi", label: "Informasi Publik" },
    { href: "/regulasi", label: "Regulasi" },
    { href: "/layanan", label: "Layanan" },
    { href: "/kontak", label: "Kontak" },
  ];

  return (
    <footer className="w-full py-8 border-t border-gray-200 bg-gray-50 md:py-12" role="contentinfo" aria-label="Footer PPID BPMP Provinsi Kaltim">
      <div className="container px-4 mx-auto max-w-7xl">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 lg:gap-12">

          {/* Organization Info */}
          <div className="space-y-4 lg:col-span-2">
            <div>
              <h3 className="mb-3 text-xl font-bold text-gray-900 md:text-lg">
                PPID BPMP Provinsi Kaltim
              </h3>
              <p className="text-sm leading-relaxed text-gray-600 md:text-base">
                Pejabat Pengelola Informasi dan Dokumentasi
              </p>
            </div>

            <address className="space-y-2 text-sm not-italic text-gray-600 md:text-base">
              <p className="flex items-start gap-2">
                <span className="font-medium">Alamat:</span>
                <span>Jl. Cipto Mangunkusumo, Samarinda, Kalimantan Timur</span>
              </p>
              <p className="flex items-center gap-2">
                <span className="font-medium">Email:</span>
                <a
                  href="mailto:<EMAIL>"
                  className="underline transition-colors text-primary-600 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50"
                >
                  <EMAIL>
                </a>
              </p>
            </address>

            {/* Government Logos */}
            <div className="pt-4">
              <p className="mb-3 text-sm font-medium text-gray-700">Kementerian Pendidikan Dasar Dan Menengah</p>
              <div className="flex items-center gap-4">
                <div className="relative w-12 h-12 md:w-14 md:h-14">
                  <Image
                    src="/tutwuri.png"
                    alt="Logo Tutwuri Handayani - Kementerian Pendidikan"
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 48px, 56px"
                    loading="lazy"
                  />
                </div>
                <div className="relative w-12 h-12 md:w-14 md:h-14">
                  <Image
                    src="/ramah.png"
                    alt="Logo Ramah Anak"
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 48px, 56px"
                    loading="lazy"
                  />
                </div>
                <div className="relative w-12 h-12 md:w-14 md:h-14">
                  <Image
                    src="/bermutu.png"
                    alt="Logo Bermutu"
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 48px, 56px"
                    loading="lazy"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 md:text-base">
              Tautan Penting
            </h4>
            <nav aria-label="Tautan Footer">
              <ul className="space-y-3">
                {navigationLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="inline-block px-3 py-2 text-sm text-gray-600 transition-all duration-200 rounded-md min-w-11 min-h-11 hover:text-primary-700 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50 md:text-base"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Social Media */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 md:text-base">
              Media Sosial
            </h4>
            <div className="flex flex-wrap gap-3" role="list" aria-label="Media Sosial">
              {socialLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={link.label}
                  className={`inline-flex items-center justify-center w-12 h-12 text-white transition-all duration-300 rounded-full bg-primary-600 hover:bg-primary-500 ${link.color} hover:scale-110 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-50 focus:scale-110`}
                  role="listitem"
                >
                  <link.icon className="text-lg" aria-hidden="true" />
                </a>
              ))}
            </div>

            {/* Additional Contact Info */}
            <div className="pt-2 space-y-1 text-sm text-gray-600">
              <p>Jam Layanan:</p>
              <p className="font-medium">Senin - Jumat: 08:00 - 16:00 WITA</p>
            </div>
          </div>
        </div>

        {/* Bottom Copyright */}
        <div className="pt-6 mt-8 text-center border-t border-gray-200 md:pt-8 md:mt-12">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm text-gray-600 md:text-base">
              © {new Date().getFullYear()} PPID BPMP Provinsi Kalimantan Timur. Seluruh hak cipta dilindungi.
            </p>
            <div className="flex flex-wrap gap-4 text-sm">
              <a
                href="/privacy"
                className="text-gray-600 transition-colors hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                Kebijakan Privasi
              </a>
              <span className="text-gray-400">•</span>
              <a
                href="/terms"
                className="text-gray-600 transition-colors hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                Syarat & Ketentuan
              </a>
              <span className="text-gray-400">•</span>
              <a
                href="/sitemap"
                className="text-gray-600 transition-colors hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                Peta Situs
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;