// components/Footer.js
"use client";
import React from 'react';
import Image from 'next/image';
import { FaFacebook, FaInstagram, FaWhatsapp, FaYoutube } from 'react-icons/fa';
import { config } from '../../lib/config';

const Footer = () => {
  const socialLinks = [
    { icon: FaFacebook, href: config.socialMedia.facebook, label: "Facebook BPMP Kaltim" },
    { icon: FaInstagram, href: config.socialMedia.instagram, label: "Instagram BPMP Kaltim" },
    { icon: FaWhatsapp, href: config.socialMedia.whatsapp, label: "WhatsApp BPMP Kaltim" },
    { icon: FaYoutube, href: config.socialMedia.youtube, label: "YouTube BPMP Kaltim" },
  ];

  return (
    <footer className="w-full py-3 border-t border-gray-200 md:py-2 bg-gray-50" role="contentinfo" aria-label="Footer PPID BPMP Provinsi <PERSON>ltim">
      <div className="container px-4 mx-auto">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-4">
          {/* Logo dan Teks */}
          <div className="text-center md:text-left">
            <h3 className="mb-1 text-lg font-bold text-gray-900 md:mb-2 md:text-base">PPID BPMP Provinsi Kaltim</h3>
            <p className="text-xs text-gray-700 md:text-xs">Pejabat Pengelola Informasi dan Dokumentasi</p>
            
            <address className="mt-2 text-xs not-italic text-gray-700 md:mt-2 md:text-xs">
              <p>Jl. Cipto Mangunkusumo, Samarinda, Kaltim</p>
              <p>Email: <a href="mailto:<EMAIL>" className="underline hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-400"><EMAIL></a></p>
            </address>
          </div>
          
          {/* Logo-logo Kemendikbud - Di tengah */}
          <div className="flex items-center justify-center order-last gap-4 md:order-none md:gap-4">
            <div className="relative w-10 h-10 md:w-12 md:h-12">
              <Image
                src="/tutwuri.png"
                alt="Logo Tutwuri Handayani"
                fill
                className="object-contain"
                sizes="(max-width: 768px) 40px, 48px"
                priority={false}
              />
            </div>
            <div className="relative w-10 h-10 md:w-12 md:h-12">
              <Image
                src="/ramah.png"
                alt="Logo Ramah Anak"
                fill
                className="object-contain"
                sizes="(max-width: 768px) 40px, 48px"
                priority={false}
              />
            </div>
            <div className="relative w-10 h-10 md:w-12 md:h-12">
              <Image
                src="/bermutu.png"
                alt="Logo Bermutu"
                fill
                className="object-contain"
                sizes="(max-width: 768px) 40px, 48px"
                priority={false}
              />
            </div>
          </div>
          
          {/* Tautan Penting */}
          {/* Navigation Links */}
          <nav aria-label="Tautan Footer">
            <h4 className="mb-1 text-sm font-semibold text-gray-900 md:mb-2 md:text-sm">Tautan Penting</h4>
            <ul className="space-y-0.5 md:space-y-0.5 text-xs md:text-xs">
              <li>
                <a
                  href="/profil"
                  className="inline-block px-3 py-2 text-gray-700 transition-colors rounded hover:text-primary-700 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50"
                  style={{ minWidth: 44, minHeight: 44 }}
                >
                  Profil
                </a>
              </li>
              <li>
                <a
                  href="/informasi"
                  className="inline-block px-3 py-2 text-gray-700 transition-colors rounded hover:text-primary-700 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50"
                  style={{ minWidth: 44, minHeight: 44 }}
                >
                  Informasi Publik
                </a>
              </li>
              <li>
                <a
                  href="/regulasi"
                  className="inline-block px-3 py-2 text-gray-700 transition-colors rounded hover:text-primary-700 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-gray-50"
                  style={{ minWidth: 44, minHeight: 44 }}
                >
                  Regulasi
                </a>
              </li>
            </ul>
            
            {/* Social Media Links */}
            <div className="mt-3 md:mt-3">
              <h4 className="mb-1 text-sm font-semibold text-gray-900 md:mb-2 md:text-sm">Media Sosial</h4>
              <div className="flex space-x-3" aria-label="Media Sosial">
                {socialLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={link.label}
                    className="inline-flex items-center justify-center text-white transition rounded-full w-11 h-11 bg-primary-600 hover:bg-primary-500 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-50"
                    data-tts-enabled
                  >
                    <link.icon className="text-xl md:text-lg" aria-hidden="true" />
                  </a>
                ))}
              </div>
            </div>
          </nav>
        </div>
        
        <div className="pt-3 mt-3 text-center border-t border-gray-200 md:pt-2 md:mt-2">
          <p className="text-xs text-gray-700 md:text-xs">
            © {new Date().getFullYear()} PPID BPMP Provinsi Kaltim. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;