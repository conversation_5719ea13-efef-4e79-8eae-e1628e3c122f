"use client";
import React, { useState } from 'react';
import Image from "next/image";
import Link from 'next/link';

const LandingPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Navbar */}
 

      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image 
          src="/Gedung.JPG" 
          alt="Gedung PPID BPMP Provinsi Kaltim" 
          fill
          style={{ objectFit: "cover", objectPosition: "center" }}
          sizes="100vw"
          quality={70}
          priority
          fetchPriority="high"
        />
      </div>
      
      {/* Overlay for readability */}
      <div className="absolute inset-0 z-10 bg-black opacity-50"></div>
      
      {/* Content */}
      <div className="relative z-20 flex items-center justify-center min-h-screen">
        <div className="max-w-3xl px-6 py-12 mx-auto text-center">
          <h1 className="mb-4 text-5xl font-bold text-white drop-shadow-lg">
            PPID BPMP Provinsi Kaltim
          </h1>
          <p className="mb-8 text-xl text-white drop-shadow-md">
            PPID adalah kepanjangan dari Pejabat Pengelola Informasi dan Dokumentasi, dimana PPID berfungsi sebagai pengelola dan penyampai dokumen yang dimiliki oleh badan publik sesuai dengan amanat UU 14/2008 tentang Keterbukaan Informasi Publik.
          </p>
         
        </div>
      </div>

      {/* Footer (hidden on mobile) removed; rely on global Footer in root layout */}
    </div>
  );
};

export default LandingPage;
