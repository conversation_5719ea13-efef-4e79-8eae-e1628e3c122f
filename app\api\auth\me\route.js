import { NextResponse } from 'next/server';
import { verifyToken } from '../../../lib/auth';
import { prisma } from '../../../lib/prisma';

export async function GET(request) {
  try {
    // Try to get token from cookie first, then fallback to Authorization header
    let token = request.cookies.get('token')?.value;
    
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    // If no token, return 200 with unauthenticated state to avoid 401 spam in logs
    if (!token) {
      return NextResponse.json({ authenticated: false, user: null });
    }

    const payload = await verifyToken(token);

    // If token invalid, also return 200 with unauthenticated state
    if (!payload) {
      return NextResponse.json({ authenticated: false, user: null });
    }

    // Get user data from database
    const user = await prisma.user.findUnique({
      where: { id: payload.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    // If user not found, treat as unauthenticated (avoid 404 noise)
    if (!user) {
      return NextResponse.json({ authenticated: false, user: null });
    }

    // Normalize user role to lowercase for consistent permissions checking
    if (user.role) {
      user.role = user.role.toLowerCase();
    }

    return NextResponse.json({
      authenticated: true,
      user,
    });
  } catch (error) {
    console.error('Auth me error:', error);
    return NextResponse.json(
      { message: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}
