'use client'
import { useState, useEffect, useMemo, useCallback, Suspense } from 'react'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import Nav from './../components/Nav'
import SafeContentRenderer from './../components/SafeContentRenderer';
import { slideInUp, shouldReduceMotion } from './../components/AnimationConfig';

// Component untuk menangani search params dengan Suspense
function RegulasiContent() {
  const [posts, setPosts] = useState([]);
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const prefersReducedMotion = shouldReduceMotion();
  const searchParams = useSearchParams();
  // Dynamic categories based on actual tags from database
  const categories = useMemo(() => {
    const categoryList = tags.map(tag => ({
      id: tag.slug,
      name: tag.name,
      tagId: tag.id
    }));
    
    // Sort categories to ensure "Regulasi" is first
    return categoryList.sort((a, b) => {
      if (a.name === 'Regulasi') return -1;
      if (b.name === 'Regulasi') return 1;
      return a.name.localeCompare(b.name);
    });
  }, [tags]);

  useEffect(() => {
    async function fetchData() {      try {
        // Fetch tags first
        const tagsResponse = await fetch('/api/tags');
        if (!tagsResponse.ok) {
          throw new Error('Failed to fetch tags');
        }
        const tagsData = await tagsResponse.json();
        setTags(tagsData.tags || []);

        // Fetch posts
        const postsResponse = await fetch('/api/posts');
        if (!postsResponse.ok) {
          throw new Error('Failed to fetch posts');
        }        const postsData = await postsResponse.json();

        // Filter published posts only - keep original structure
        const publishedPosts = postsData.filter(post => post.published);

        setPosts(publishedPosts);      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [searchParams]);

  // Set selected tab based on URL parameter when categories are available
  useEffect(() => {
    if (categories.length > 0) {
      const tabParam = searchParams.get('tab');

      if (tabParam) {
        // Find index of tab that matches category id or name
        const categoryIndex = categories.findIndex(cat =>
          cat.id === tabParam ||
          cat.name.toLowerCase() === tabParam.toLowerCase()
        );

        if (categoryIndex !== -1) {
          setSelectedIndex(categoryIndex);
        }
      }
    }
  }, [categories, searchParams]);
  // Get posts by category using comprehensive matching
  const getPostsByCategory = useCallback((categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    if (!category) {
      return [];
    }

    const filteredPosts = posts.filter(post => {
      // Check if post has tagsonposts array
      if (!post.tagsonposts || !Array.isArray(post.tagsonposts)) {
        return false;
      }

      const hasMatch = post.tagsonposts.some(tagRelation => {
        // tagRelation structure from API: { postId, tagId, tag: { id, name, slug } }
        if (!tagRelation.tag) {
          return false;
        }

        const tagSlug = (tagRelation.tag.slug || '').toLowerCase().trim();
        const tagId = tagRelation.tag.id;
        const tagName = (tagRelation.tag.name || '').toLowerCase().trim();
        const categorySlug = categoryId.toLowerCase().trim();
        const categoryName = category.name.toLowerCase().trim();

        // Match by tag ID (most reliable) or slug or name
        const matchById = tagId === category.tagId;
        const matchBySlug = tagSlug === categorySlug;
        const matchByName = tagName === categoryName;

        return matchById || matchBySlug || matchByName;
      });

      return hasMatch;
    });    return filteredPosts;
  }, [posts, categories]);

  if (loading) {
    return (
      <>
        <Nav/>
        <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 text-sm font-semibold leading-6 transition duration-150 ease-in-out bg-white rounded-md shadow text-primary-600">
              <svg className="w-5 h-5 mr-3 -ml-1 animate-spin text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Memuat data regulasi dan informasi publik...
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen">
        <Nav/>
        <div className="flex-grow w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
          <div className="text-center text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Nav/>
      <div className="flex-grow w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <motion.div 
          className="mb-12 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-primary-800 sm:text-5xl">
            Regulasi dan Informasi Publik
          </h1>
          <p className="max-w-3xl mx-auto text-lg leading-relaxed text-gray-600">
            Akses mudah ke berbagai regulasi, peraturan, dan informasi publik 
            yang tersedia di BPMP Provinsi Kalimantan Timur
          </p>
        </motion.div>
        
        {categories.length > 0 ? (
          <TabGroup selectedIndex={selectedIndex} onChange={setSelectedIndex}>
            <TabList className="flex flex-wrap gap-2 p-2 mb-8 bg-white border border-gray-200 shadow-sm rounded-xl">
              {categories.map((category) => (
                <Tab
                  key={category.id}
                  className={({ selected }) =>
                    `flex-1 min-w-[120px] rounded-lg py-3 px-4 text-sm font-medium leading-5 text-center
                    ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2
                    transition-all duration-200 ease-in-out
                    ${
                      selected
                        ? 'bg-primary-600 text-white shadow-md transform scale-[1.02]'
                        : 'text-primary-700 hover:bg-primary-50 hover:text-primary-800 border border-transparent hover:border-primary-200'
                    }`
                  }
                >
                  <span className="block truncate">{category.name}</span>
                  <span className="block mt-1 text-xs opacity-75">
                    {getPostsByCategory(category.id).length} item
                  </span>
                </Tab>
              ))}
            </TabList>
            <TabPanels className="mt-4">
              {categories.map((category, idx) => {
                const categoryPosts = getPostsByCategory(category.id);
                
                return (                  <TabPanel
                    key={`panel-${idx}`}
                    className="overflow-hidden bg-white border border-gray-200 shadow-sm rounded-xl"
                  >
                    <motion.div
                      key={`motion-${category.id}-${selectedIndex}`}
                      {...(prefersReducedMotion ? {} : slideInUp)}
                      transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
                    >
                      {categoryPosts.length > 0 ? (
                        <div className="divide-y divide-gray-100">
                          {categoryPosts.map((post, index) => (
                            <div key={`${post.id}-${category.id}-${selectedIndex}`} 
                                 className="p-6 transition-colors duration-200 hover:bg-gray-50">
                              {/* Post header with better spacing */}
                              <div className="pb-3 mb-4 border-b border-gray-100">
                                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                                  {post.title}
                                </h3>
                              </div>
                              
                              {/* Content with improved styling */}
                              <div className="prose-content">
                                <SafeContentRenderer
                                  content={post.content}
                                  className="prose-primary max-w-none"
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        // Empty state dengan design yang lebih baik
                        <div className="py-16 text-center">
                          <div className="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full">
                            <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                          </div>
                          <h3 className="mb-3 text-xl font-medium text-gray-900">Belum Ada Konten</h3>
                          <p className="max-w-sm mx-auto mb-2 text-gray-600">Belum ada postingan dengan kategori "{category.name}".</p>
                          <p className="text-sm text-gray-500">Konten akan ditambahkan segera. Silakan cek kembali nanti.</p>
                        </div>
                      )}
                    </motion.div>
                  </TabPanel>
                );
              })}
            </TabPanels>
          </TabGroup>
        ) : (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <div className="text-center">
              <p className="mb-4 text-gray-600">Belum ada kategori regulasi yang tersedia.</p>
              {tags.length === 0 ? (
                <p className="text-sm text-gray-500">
                  Tidak ada tag yang ditemukan. Silakan buat tag terlebih dahulu di dashboard admin.
                </p>
              ) : (
                <p className="text-sm text-gray-500">
                  Tag tersedia: {tags.map(tag => tag.name).join(', ')}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Loading component untuk Suspense fallback
function RegulasiLoadingFallback() {
  return (
    <div className="flex flex-col min-h-screen">
      <Nav />
      <div className="container flex-grow px-4 py-8 mx-auto">
        <div className="flex flex-col items-center justify-center min-h-[400px]" role="status" aria-live="polite">
          <div className="w-12 h-12 mb-4 border-4 border-blue-500 rounded-full border-t-transparent animate-spin" aria-hidden="true"></div>
          <p className="text-blue-600">Memuat halaman regulasi...</p>
        </div>
      </div>
    </div>
  );
}

// Main component dengan Suspense boundary
export default function RegulasiPage() {
  return (
    <Suspense fallback={<RegulasiLoadingFallback />}>
      <RegulasiContent />
    </Suspense>
  );
}
