'use client';

/**
 * Hook untuk validasi konten CKEditor
 * Menyediakan fungsi validasi yang dapat digunakan di berbagai komponen
 */
export function useContentValidation() {
  const validateContent = (content) => {
    // Check if content is empty
    if (!content || content.trim() === '') {
      return 'Konten tidak boleh kosong';
    }

    // Strip HTML tags untuk menghitung karakter yang sebenarnya
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    
    // Check minimum length
    if (textContent.length < 10) {
      return 'Konten minimal 10 karakter';
    }

    // Check maximum length (optional - untuk mencegah konten terlalu panjang)
    if (textContent.length > 50000) {
      return 'Konten maksimal 50.000 karakter';
    }

    return null; // No error
  };

  const validateTitle = (title) => {
    if (!title || title.trim() === '') {
      return 'Judul tidak boleh kosong';
    }

    if (title.trim().length < 3) {
      return 'Judul minimal 3 karakter';
    }

    if (title.length > 200) {
      return 'Judul maksimal 200 karakter';
    }

    return null;
  };

  const validateSlug = (slug) => {
    if (!slug || slug.trim() === '') {
      return 'Slug tidak boleh kosong';
    }

    // Check if slug contains only valid characters
    const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    if (!slugPattern.test(slug)) {
      return 'Slug hanya boleh mengandung huruf kecil, angka, dan tanda hubung';
    }

    if (slug.length < 3) {
      return 'Slug minimal 3 karakter';
    }

    if (slug.length > 100) {
      return 'Slug maksimal 100 karakter';
    }

    return null;
  };

  const validateExcerpt = (excerpt) => {
    if (excerpt && excerpt.length > 500) {
      return 'Ringkasan maksimal 500 karakter';
    }

    return null;
  };

  const validateAll = (formData) => {
    const errors = {};

    const titleError = validateTitle(formData.title);
    if (titleError) errors.title = titleError;

    const slugError = validateSlug(formData.slug);
    if (slugError) errors.slug = slugError;

    const contentError = validateContent(formData.content);
    if (contentError) errors.content = contentError;

    const excerptError = validateExcerpt(formData.excerpt);
    if (excerptError) errors.excerpt = excerptError;

    return {
      errors,
      isValid: Object.keys(errors).length === 0
    };
  };

  return {
    validateContent,
    validateTitle,
    validateSlug,
    validateExcerpt,
    validateAll
  };
}
