# Catatan Perintah Penting Prisma & Database

Berikut adalah perintah-perintah yang aman dan perlu dijalankan untuk sinkronisasi database tanpa menghapus data:

## 1. Sinkronisasi Struktur Database

**Menyesuaikan struktur tabel dengan schema.prisma tanpa menghapus data:**

```
npx prisma migrate dev --name sync-keberatan
```
Atau jika tidak ingin membuat migration baru:
```
npx prisma db push
```

> Keduanya TIDAK akan menghapus data lama di tabel.

## 2. Mengecek Status Migrasi

```
npx prisma migrate status
```

## 3. Membuka Prisma Studio (GUI untuk cek data)

```
npx prisma studio
```

## 4. Reset Database (Hati-hati! Akan menghapus semua data)

```
npx prisma migrate reset
```
> **Jangan jalankan ini di database produksi jika tidak ingin kehilangan data!**

## 5. Membuat Tabel AuditLog Secara Manual (Jika diperlukan)

```sql
CREATE TABLE AuditLog (
  id INT AUTO_INCREMENT PRIMARY KEY,
  entityType VARCHAR(50) NOT NULL,
  entityId VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  field VARCHAR(100) NOT NULL,
  oldValue TEXT,
  newValue TEXT,
  userId VARCHAR(50) NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

**Catatan:**
- Pastikan backup database sebelum melakukan perubahan besar.
- Untuk deployment di Linux, perhatikan case-sensitive pada nama tabel.
- Jika ada error tabel tidak ditemukan, pastikan migrasi sudah dijalankan dan nama tabel sesuai schema.
