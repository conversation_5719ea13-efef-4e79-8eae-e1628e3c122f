"use client";

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  HomeIcon,
  DocumentTextIcon,
  ChartBarIcon,
  UserGroupIcon,
  CalendarIcon,
  Cog6ToothIcon,
  ArrowUpTrayIcon,
  ArrowLeftOnRectangleIcon,
  NewspaperIcon,
  PencilSquareIcon,
  ExclamationTriangleIcon,
  TagIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';

export default function Sidebar({ onLogout, theme = 'dark' }) {
  const pathname = usePathname();
  
  // Gunakan localStorage untuk menyimpan status collapsed
  const [collapsed, setCollapsed] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebarCollapsed');
      return saved !== null ? JSON.parse(saved) : false;
    }
    return false;
  });
  
  // Update localStorage saat status berubah
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebarCollapsed', JSON.stringify(collapsed));
    }
  }, [collapsed]);
  
  // Tema styling
  const themeStyles = {
    dark: 'bg-gray-800 text-white',
    light: 'bg-white text-gray-800 border-r border-gray-200'
  };

  const isLight = theme === 'light';
  const styles = {
    headerBorder: isLight ? 'border-gray-200' : 'border-gray-700',
    toggleHover: isLight ? 'hover:bg-gray-100' : 'hover:bg-gray-700',
    itemActive: isLight ? 'bg-blue-50 text-blue-700' : 'bg-gray-700 text-white',
    itemInactive: isLight ? 'text-gray-700 hover:bg-blue-50 hover:text-blue-700' : 'text-gray-300 hover:bg-gray-700',
    groupText: isLight ? 'text-gray-600' : 'text-gray-300',
    logoutBtn: isLight ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-300 hover:bg-gray-700',
  };

  // Link untuk membuka modal via URL (demo)
  const modalHref = {
    pathname,
    query: {
      modal: 'simple',
      modalData: JSON.stringify({
        title: 'Informasi',
        content: 'Ini modal yang dibuka via URL.'
      })
    }
  };

  // Navigation configuration
  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    {
      name: 'Konten',
      icon: DocumentTextIcon,
      children: [
        { name: 'Postingan', href: '/dashboard/posts', icon: NewspaperIcon },
        { name: 'Dokumen', href: '/dashboard/documents', icon: DocumentTextIcon },
        { name: 'Upload File', href: '/dashboard/upload', icon: ArrowUpTrayIcon },
        { name: 'Kelola Tag', href: '/dashboard/tags', icon: TagIcon },
        // Buka Modal (Demo) dipindah ke dalam grup Konten
        { name: 'Buka Modal (Demo)', href: modalHref, icon: PencilSquareIcon },
      ]
    },
    { name: 'Daftar Permohonan PPID', href: '/dashboard/permohonan', icon: ClipboardDocumentListIcon },
    { name: 'Statistik', href: '/dashboard/statistics', icon: ChartBarIcon },
    { name: 'Pengguna', href: '/dashboard/users', icon: UserGroupIcon },
    { name: 'Kegiatan', href: '/dashboard/events', icon: CalendarIcon },
    { name: 'Log Sistem', href: '/dashboard/logs', icon: ExclamationTriangleIcon },
    { name: 'Audit Log', href: '/admin/auditlog-page', icon: ExclamationTriangleIcon },
    { name: 'Pengaturan', href: '/dashboard/settings', icon: Cog6ToothIcon },
  ];

  return (
    <motion.div 
      className={`${themeStyles[theme]} transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'} min-h-screen`}
      layout
      transition={{ duration: 0.2 }}
    >
    <div className={`flex items-center justify-between p-4 border-b ${styles.headerBorder}`}>
        {!collapsed && <h2 className="text-xl font-bold">Admin Panel</h2>}
        <button 
          onClick={() => setCollapsed(!collapsed)} 
      className={`p-1 rounded-md ${styles.toggleHover}`}
        >
          {collapsed ? '→' : '←'}
        </button>
      </div>
      
      <nav className="mt-5">
        <ul className="space-y-2">
          {navigation.map((item) => (
            <li key={item.name}>
              {item.href ? (
                // Item dengan href langsung (tanpa submenu)
                <Link 
                  href={item.href}
                  className={`flex items-center w-full px-4 py-2 transition-colors ${
                    pathname === item.href ? styles.itemActive : styles.itemInactive
                  }`}
                >
                  <item.icon className="w-6 h-6 mr-3" />
                  {!collapsed && <span>{item.name}</span>}
                </Link>
              ) : (
                // Item dengan children (submenu)
                <div>
                  <div className={`flex items-center px-4 py-2 ${styles.groupText}`}>
                    <item.icon className="w-6 h-6 mr-3" />
                    {!collapsed && <span>{item.name}</span>}
                  </div>
                  
                  {!collapsed && item.children && (
                    <ul className="pl-8 mt-1 space-y-1">
                      {item.children.map((child) => (
                        <li key={child.name}>
                          <Link
                            href={child.href}
                            className={`flex items-center w-full px-4 py-2 transition-colors ${
                              pathname === child.href ? styles.itemActive : styles.itemInactive
                            }`}
                          >
                            <child.icon className="w-5 h-5 mr-3" />
                            <span>{child.name}</span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </li>
          ))}

          <li className="px-4 pt-6">
            <button
              onClick={onLogout}
              className={`flex items-center w-full px-4 py-2 transition-colors ${styles.logoutBtn}`}
            >
              <ArrowLeftOnRectangleIcon className="w-6 h-6 mr-3" />
              {!collapsed && <span>Logout</span>}
            </button>
          </li>
        </ul>
      </nav>
    </motion.div>
  );
}










