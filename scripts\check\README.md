# 🔍 Check Scripts Collection

This folder contains all check and verification scripts used for testing, validation, and diagnostics of the PPID application.

## 📋 Script Categories

### 🔍 **Data Validation Scripts**
- `check-all-posts.js` - Check all posts data integrity
- `check-content.js` - Validate content structure and format
- `check-current-data.js` - Check current data state
- `check-data.js` - General data validation
- `check-dashboard-posts.js` - Validate dashboard posts

### 🎨 **UI/UX Validation Scripts**
- `check-css-build.js` - Validate CSS build process and styling
- `check-logo-size.js` - Check logo dimensions and optimization

### 📦 **Import/Dependencies Scripts**
- `check-imports.js` - Validate import statements and dependencies

## 🔧 **Usage Instructions**

### Running Scripts:
```bash
cd d:\web2025\ppid\scripts\check
node script-name.js
```

### From Root Directory:
```bash
cd d:\web2025\ppid
node scripts\check\script-name.js
```

### Run All Checks:
```bash
cd scripts\check
for %f in (*.js) do node %f
```

## 📊 **Script Descriptions**

| Script | Purpose | Category | Status |
|--------|---------|----------|--------|
| `check-all-posts.js` | Check all posts integrity | Data Validation | Ready |
| `check-content.js` | Content structure validation | Data Validation | Active |
| `check-css-build.js` | CSS build validation | UI/UX | Active |
| `check-current-data.js` | Current data state check | Data Validation | Active |
| `check-dashboard-posts.js` | Dashboard posts validation | Data Validation | Ready |
| `check-data.js` | General data validation | Data Validation | Ready |
| `check-imports.js` | Import statements check | Dependencies | Active |
| `check-logo-size.js` | Logo optimization check | UI/UX | Active |

## 🎯 **Development Workflow**

### 1. **Pre-Development Checks:**
```bash
node check-imports.js
node check-data.js
```

### 2. **Content Validation:**
```bash
node check-content.js
node check-all-posts.js
node check-current-data.js
```

### 3. **UI/UX Validation:**
```bash
node check-css-build.js
node check-logo-size.js
```

### 4. **Dashboard Checks:**
```bash
node check-dashboard-posts.js
```

## 🚀 **Quick Check Commands**

### **Full System Check:**
```bash
cd scripts\check
echo "Running full system check..."
node check-data.js
node check-imports.js
node check-content.js
node check-css-build.js
echo "Check complete!"
```

### **Data Integrity Check:**
```bash
cd scripts\check
node check-current-data.js
node check-all-posts.js
node check-dashboard-posts.js
```

### **Build Validation:**
```bash
cd scripts\check
node check-css-build.js
node check-logo-size.js
```

## 📁 **File Organization**

All check scripts are now centralized in this folder for:
- ✅ **Better organization** and discovery
- ✅ **Easy maintenance** and updates
- ✅ **Clear categorization** by purpose
- ✅ **Consistent location** for validation scripts

## 🔍 **Troubleshooting**

### **Common Issues:**
1. **Script not found** - Ensure you're in the correct directory
2. **Permission errors** - Run with appropriate permissions
3. **Missing dependencies** - Run `npm install` first

### **Debug Mode:**
Add `--verbose` flag or set `DEBUG=true` environment variable for detailed output.

## 📈 **Monitoring & Reporting**

These scripts can be integrated into:
- CI/CD pipelines
- Pre-commit hooks
- Automated testing workflows
- Health monitoring systems

---

*Scripts organized: August 8, 2025*  
*Total check scripts: 8 files*
