@echo off
echo ===============================================
echo    ACCORDION PRESERVATION FIX - VERIFICATION
echo ===============================================

echo.
echo [1/4] Checking Confirmation Modal Implementation...
findstr /n "showConfirmModal" app\components\SimpleHTMLEditor.jsx
echo.

echo [2/4] Checking Switch Prevention Logic...
findstr /A "sourceCode.includes.*details" app\components\SimpleHTMLEditor.jsx
echo.

echo [3/4] Checking Modal UI Components...
findstr /A "Konfirmasi Beralih ke Visual Mode" app\components\SimpleHTMLEditor.jsx
echo.

echo [4/4] Verifying Confirm/Cancel Functions...
findstr /n "confirmSwitchToVisual\|cancelSwitchToVisual" app\components\SimpleHTMLEditor.jsx
echo.

echo ===============================================
echo ✅ ACCORDION PRESERVATION FIX IMPLEMENTED!
echo ===============================================
echo.
echo 🛡️ New Protection Mechanism:
echo 1. Detection: Source has accordion ✅
echo 2. Prevention: Modal confirmation ✅
echo 3. User Choice: Stay or proceed ✅
echo 4. Clear Warning: Loss explanation ✅
echo.
echo 🔄 New Workflow:
echo Source Mode → Click Visual Tab → Confirmation Modal
echo    ↓                ↓                    ↓
echo Accordion     Shows Warning      User Chooses
echo Detected      About Loss        Stay/Proceed
echo.
echo 🎯 Result: NO MORE ACCIDENTAL ACCORDION LOSS!
echo.
pause
