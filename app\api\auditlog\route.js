import { NextResponse } from "next/server";
import { prisma } from "../../lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "../../lib/auth";

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: '<PERSON><PERSON><PERSON> ditolak' }, { status: 401 });
    const userData = await verifyToken(token);
    if (!userData || (userData.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Tidak berizin' }, { status: 403 });
    }
    // Only show status/catatan/tanggapan changes for permohonan & keberatan
    const logs = await prisma.auditLog.findMany({
      where: {
        entityType: { in: ["permohonan", "keberatan"] },
        field: { in: ["status", "catatanAdmin", "tanggapanAdmin"] },
      },
      orderBy: { createdAt: "desc" },
      take: 200,
    });
    return NextResponse.json({ logs });
  } catch (e) {
    return NextResponse.json({ logs: [], error: "Gagal memuat audit log" }, { status: 500 });
  }
}
