"use client";

import { useRouter } from 'next/navigation';
import { useEffect, useState, Fragment } from 'react';
import { Dialog, Disclosure, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  BellIcon,
  XMarkIcon,
  ChevronDownIcon,
  UserCircleIcon,
  ArrowLeftOnRectangleIcon,
  HomeIcon,
  DocumentTextIcon,
  ChartBarIcon,
  UserGroupIcon,
  CalendarIcon,
  Cog6ToothIcon,
  ArrowUpTrayIcon,
  NewspaperIcon,
  TagIcon,
  ClipboardDocumentListIcon,
  ExclamationTriangleIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../context/AuthContext';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export default function DashboardLayout({ children }) {
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Href untuk membuka modal via URL (global URL modal host)
  const modalHref = {
    pathname,
    query: {
      modal: 'simple',
      modalData: JSON.stringify({ title: 'Informasi', content: 'Ini modal yang dibuka via URL.' })
    }
  };

  // Navigation configuration
  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: pathname === '/dashboard' },
    {
      name: 'Konten',
      icon: DocumentTextIcon,
      current: pathname.startsWith('/dashboard/posts') || pathname.startsWith('/dashboard/documents') || pathname.startsWith('/dashboard/upload') || pathname.startsWith('/dashboard/tags') || pathname.startsWith('/dashboard/urlmodals'),
      children: [
        { name: 'Postingan', href: '/dashboard/posts', icon: NewspaperIcon, current: pathname.startsWith('/dashboard/posts') },
        { name: 'Dokumen', href: '/dashboard/documents', icon: DocumentTextIcon, current: pathname.startsWith('/dashboard/documents') },
        { name: 'Upload File', href: '/dashboard/upload', icon: ArrowUpTrayIcon, current: pathname.startsWith('/dashboard/upload') },
        { name: 'Kelola Tag', href: '/dashboard/tags', icon: TagIcon, current: pathname.startsWith('/dashboard/tags') },
        { name: 'Daftar Modal URL', href: '/dashboard/urlmodals', icon: PencilSquareIcon, current: pathname.startsWith('/dashboard/urlmodals') },
        { name: 'Buat Modal (Baru)', href: '/dashboard/urlmodals/new', icon: PencilSquareIcon, current: pathname.startsWith('/dashboard/urlmodals/new') },
        // Item demo untuk membuka modal via URL
        { name: 'Buka Modal (Demo)', href: modalHref, icon: PencilSquareIcon, current: false },
      ]
    },
    { name: 'Daftar Permohonan PPID', href: '/dashboard/permohonan', icon: ClipboardDocumentListIcon, current: pathname.startsWith('/dashboard/permohonan') },
    { name: 'Daftar Keberatan Permohonan PPID', href: '/dashboard/keberatan', icon: ClipboardDocumentListIcon, current: pathname.startsWith('/dashboard/permohonan') },
    { name: 'Statistik', href: '/dashboard/statistics', icon: ChartBarIcon, current: pathname.startsWith('/dashboard/statistics') },
    { name: 'Pengguna', href: '/dashboard/users', icon: UserGroupIcon, current: pathname.startsWith('/dashboard/users') },
    { name: 'Kegiatan', href: '/dashboard/events', icon: CalendarIcon, current: pathname.startsWith('/dashboard/events') },
    { name: 'Log Sistem', href: '/dashboard/logs', icon: ExclamationTriangleIcon, current: pathname.startsWith('/dashboard/logs') },
    { name: 'Pengaturan', href: '/dashboard/settings', icon: Cog6ToothIcon, current: pathname.startsWith('/dashboard/settings') },
  ];

  // Handle redirect to login when user is not authenticated
  useEffect(() => {
    if (!loading && (!user || !user.id)) {
      router.push('/login');
    }
  }, [loading, user, router]);
  
  // Loading state selama autentikasi
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
        <div className="w-12 h-12 mb-4 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
        <p className="text-gray-600">Memuat...</p>
        <p className="mt-2 text-sm text-gray-500">Memeriksa autentikasi...</p>
      </div>
    );
  }

  // Jika tidak login, show loading message while redirecting
  if (!user || !user.id) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
        <div className="w-8 h-8 mb-4 border-4 border-gray-200 rounded-full border-t-blue-500 animate-spin"></div>
        <p className="text-gray-600">Mengarahkan ke halaman login...</p>
      </div>
    );
  }
  // Handle logout function
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative flex flex-1 w-full max-w-xs mr-16">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute top-0 flex justify-center w-16 pt-5 left-full">
                    <button type="button" className="-m-2.5 p-2.5" onClick={() => setSidebarOpen(false)}>
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="w-6 h-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                {/* Mobile Sidebar content */}
                <div className="flex flex-col px-6 pb-2 overflow-y-auto bg-white grow gap-y-5 ring-1 ring-white/10">
                  <div className="flex items-center h-16 shrink-0">
                    <h2 className="text-xl font-bold text-gray-900">PPID BPMP</h2>
                  </div>
                  <nav className="flex flex-col flex-1">
                    <ul role="list" className="flex flex-col flex-1 gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {navigation.map((item) => (
                            <li key={item.name}>
                              {!item.children ? (
                                <Link
                                  href={item.href}
                                  className={classNames(
                                    item.current
                                      ? 'bg-blue-50 text-blue-700'
                                      : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                                    'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                                  )}
                                  onClick={() => setSidebarOpen(false)}
                                >
                                  <item.icon
                                    className={classNames(
                                      item.current ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700',
                                      'h-6 w-6 shrink-0'
                                    )}
                                    aria-hidden="true"
                                  />
                                  {item.name}
                                </Link>
                              ) : (
                                <Disclosure as="div">
                                  {({ open }) => (
                                    <>
                                      <Disclosure.Button
                                        className={classNames(
                                          item.current
                                            ? 'bg-blue-50 text-blue-700'
                                            : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                                          'flex items-center w-full text-left rounded-md p-2 gap-x-3 text-sm leading-6 font-semibold'
                                        )}
                                      >
                                        <item.icon
                                          className={classNames(
                                            item.current ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700',
                                            'h-6 w-6 shrink-0'
                                          )}
                                          aria-hidden="true"
                                        />
                                        {item.name}
                                        <ChevronDownIcon
                                          className={classNames(
                                            open ? 'rotate-180' : '',
                                            'ml-auto h-5 w-5 shrink-0 transition-transform'
                                          )}
                                          aria-hidden="true"
                                        />
                                      </Disclosure.Button>
                                      <Disclosure.Panel as="ul" className="px-2 mt-1">
                                        {item.children.map((subItem) => (
                                          <li key={subItem.name}>
                                            <Link
                                              href={subItem.href}
                                              className={classNames(
                                                subItem.current
                                                  ? 'bg-blue-50 text-blue-700'
                                                  : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                                                'block rounded-md py-2 pr-2 pl-9 text-sm leading-6'
                                              )}
                                              onClick={() => setSidebarOpen(false)}
                                            >
                                              {subItem.name}
                                            </Link>
                                          </li>
                                        ))}
                                      </Disclosure.Panel>
                                    </>
                                  )}
                                </Disclosure>
                              )}
                            </li>
                          ))}
                        </ul>
                      </li>
                      <li className="mt-auto -mx-6">
                        <button
                          onClick={handleLogout}
                          className="flex items-center w-full px-6 py-3 text-sm font-semibold leading-6 text-left text-gray-900 gap-x-4 hover:bg-gray-50"
                        >
                          <ArrowLeftOnRectangleIcon className="w-6 h-6" aria-hidden="true" />
                          <span>Logout</span>
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-col px-6 overflow-y-auto bg-white border-r border-gray-200 grow gap-y-5">
          <div className="flex items-center h-16 shrink-0">
            <h2 className="text-xl font-bold text-gray-900">PPID BPMP</h2>
          </div>
          <nav className="flex flex-col flex-1">
            <ul role="list" className="flex flex-col flex-1 gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      {!item.children ? (
                        <Link
                          href={item.href}
                          className={classNames(
                            item.current
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                            'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                          )}
                        >
                          <item.icon
                            className={classNames(
                              item.current ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700',
                              'h-6 w-6 shrink-0'
                            )}
                            aria-hidden="true"
                          />
                          {item.name}
                        </Link>
                      ) : (
                        <Disclosure as="div">
                          {({ open }) => (
                            <>
                              <Disclosure.Button
                                className={classNames(
                                  item.current
                                    ? 'bg-blue-50 text-blue-700'
                                    : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                                  'flex items-center w-full text-left rounded-md p-2 gap-x-3 text-sm leading-6 font-semibold'
                                )}
                              >
                                <item.icon
                                  className={classNames(
                                    item.current ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700',
                                    'h-6 w-6 shrink-0'
                                  )}
                                  aria-hidden="true"
                                />
                                {item.name}
                                <ChevronDownIcon
                                  className={classNames(
                                    open ? 'rotate-180' : '',
                                    'ml-auto h-5 w-5 shrink-0 transition-transform'
                                  )}
                                  aria-hidden="true"
                                />
                              </Disclosure.Button>
                              <Disclosure.Panel as="ul" className="px-2 mt-1">
                                {item.children.map((subItem) => (
                                  <li key={subItem.name}>
                                    <Link
                                      href={subItem.href}
                                      className={classNames(
                                        subItem.current
                                          ? 'bg-blue-50 text-blue-700'
                                          : 'text-gray-700 hover:text-blue-700 hover:bg-blue-50',
                                        'block rounded-md py-2 pr-2 pl-9 text-sm leading-6'
                                      )}
                                    >
                                      {subItem.name}
                                    </Link>
                                  </li>
                                ))}
                              </Disclosure.Panel>
                            </>
                          )}
                        </Disclosure>
                      )}
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto -mx-6">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-6 py-3 text-sm font-semibold leading-6 text-left text-gray-900 gap-x-4 hover:bg-gray-50"
                >
                  <ArrowLeftOnRectangleIcon className="w-6 h-6" aria-hidden="true" />
                  <span>Logout</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Header */}
        <div className="sticky top-0 z-40 flex items-center h-16 px-4 bg-white border-b border-gray-200 shadow-sm shrink-0 gap-x-4 sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="w-6 h-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div className="w-px h-6 bg-gray-200 lg:hidden" aria-hidden="true" />

          <div className="flex self-stretch flex-1 gap-x-4 lg:gap-x-6">
            <div className="flex items-center flex-1">
              <h1 className="text-lg font-semibold leading-6 text-gray-900">
                Dashboard
              </h1>
            </div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <button type="button" className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500">
                <span className="sr-only">View notifications</span>
                <BellIcon className="w-6 h-6" aria-hidden="true" />
              </button>

              {/* Separator */}
              <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true" />

              {/* Profile dropdown */}
              <Menu as="div" className="relative">
                <Menu.Button className="-m-1.5 flex items-center p-1.5">
                  <span className="sr-only">Open user menu</span>
                  <UserCircleIcon className="w-8 h-8 text-gray-400" aria-hidden="true" />
                  <span className="hidden lg:flex lg:items-center">
                    <span className="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">
                      {user?.username || 'User'}
                    </span>
                    <ChevronDownIcon className="w-5 h-5 ml-2 text-gray-400" aria-hidden="true" />
                  </span>
                </Menu.Button>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                    <Menu.Item>
                      {({ active }) => (
                        <Link
                          href="/dashboard/settings"
                          className={classNames(
                            active ? 'bg-gray-50' : '',
                            'block px-3 py-1 text-sm leading-6 text-gray-900'
                          )}
                        >
                          Pengaturan
                        </Link>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={handleLogout}
                          className={classNames(
                            active ? 'bg-gray-50' : '',
                            'block w-full text-left px-3 py-1 text-sm leading-6 text-gray-900'
                          )}
                        >
                          Logout
                        </button>
                      )}
                    </Menu.Item>
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>
        </div>

        {/* Main content area */}
        <main className="py-6">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
