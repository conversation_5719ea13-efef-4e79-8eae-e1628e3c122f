// PATCH: Single admin update for keberatan (status, catatanAdmin, tanggapanAdmin) with audit logging
export async function PATCH(request) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: 'Akses ditolak' }, { status: 401 });
    const userData = await verifyToken(token);
    if (!userData || (userData.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Tidak berizin' }, { status: 403 });
    }
    const body = await request.json();
    const { id, status, catatanAdmin, tanggapanAdmin } = body;
    if (!id) return NextResponse.json({ error: 'ID wajib diisi' }, { status: 400 });
    const valid = ['pending','diproses','selesai','ditolak'];
    if (status && !valid.includes(status)) {
      return NextResponse.json({ error: 'Status tidak valid' }, { status: 400 });
    }
    // Fetch old values for audit
    const old = await prisma.keberatanInformasi.findUnique({ where: { id } });
    if (!old) return NextResponse.json({ error: 'Data tidak ditemukan' }, { status: 404 });
    const updatedKeberatan = await prisma.keberatanInformasi.update({
      where: { id },
      data: {
        ...(status && { status }),
        ...(catatanAdmin !== undefined && { catatanAdmin }),
        ...(tanggapanAdmin !== undefined && { tanggapanAdmin }),
        adminId: userData.id,
        updatedAt: new Date()
      },
      select: {
        id: true,
        status: true,
        catatanAdmin: true,
        tanggapanAdmin: true,
        updatedAt: true
      }
    });
    // Audit log for changed fields
    try {
      const changes = [];
      if (status && old.status !== status) changes.push({ field: 'status', oldValue: old.status, newValue: status });
      if (catatanAdmin !== undefined && old.catatanAdmin !== catatanAdmin) changes.push({ field: 'catatanAdmin', oldValue: old.catatanAdmin, newValue: catatanAdmin });
      if (tanggapanAdmin !== undefined && old.tanggapanAdmin !== tanggapanAdmin) changes.push({ field: 'tanggapanAdmin', oldValue: old.tanggapanAdmin, newValue: tanggapanAdmin });
      for (const c of changes) {
        await prisma.auditLog.create({
          data: {
            entityType: 'keberatan',
            entityId: id,
            action: 'update',
            field: c.field,
            oldValue: c.oldValue,
            newValue: c.newValue,
            userId: userData.id,
          }
        });
      }
    } catch (e) { console.error('AuditLog error', e); }

    return NextResponse.json({
      success: true,
      message: 'Keberatan berhasil diupdate',
      data: updatedKeberatan
    });
  } catch (e) {
    console.error('Error single update keberatan:', e);
    return NextResponse.json({ error: 'Kesalahan server' }, { status: 500 });
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { cookies } from 'next/headers';
import { verifyToken } from '../../lib/auth';

const prisma = new PrismaClient();

// Generate 6-digit ID
function generateId() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Ensure unique ID
async function generateUniqueId() {
  let id;
  let exists = true;
  
  while (exists) {
    id = generateId();
    const existing = await prisma.keberatanInformasi.findUnique({
      where: { id }
    });
    exists = !!existing;
  }
  
  return id;
}

// Replaced by combined POST above

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const statusParam = searchParams.get('status'); // comma-separated
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortDir = (searchParams.get('sortDir') || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc';

    const where = {};
    if (statusParam && statusParam !== '') {
      const statuses = statusParam.split(',').map(s => s.trim()).filter(Boolean);
      if (statuses.length === 1) where.status = statuses[0];
      else if (statuses.length > 1) where.status = { in: statuses };
    }
    if (search && search.trim() !== '') {
      where.OR = [
        { id: { contains: search } },
        { namaSesuaiKtp: { contains: search, mode: 'insensitive' } },
        { alamatEmail: { contains: search, mode: 'insensitive' } },
        { topikKeberatan: { contains: search, mode: 'insensitive' } },
        { maksudKeberatan: { contains: search, mode: 'insensitive' } },
        { alasanKeberatan: { contains: search, mode: 'insensitive' } },
      ];
    }
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = new Date(dateFrom + 'T00:00:00');
      if (dateTo) where.createdAt.lte = new Date(dateTo + 'T23:59:59');
    }

    const total = await prisma.keberatanInformasi.count({ where });
    const keberatan = await prisma.keberatanInformasi.findMany({
      where,
      orderBy: {
        [(['createdAt', 'tanggalPermohonan', 'status', 'namaSesuaiKtp'].includes(sortBy) ? sortBy : 'createdAt')]: sortDir
      },
      skip: (page - 1) * limit,
      take: limit,
    });
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: keberatan,
      pagination: { page, limit, total, totalPages, hasNext: page < totalPages, hasPrev: page > 1 }
    });

  } catch (error) {
    console.error('Error fetching keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  // This route handles creation when content-type is multipart/form-data
  // and bulk updates when JSON with action is provided.
  const contentType = request.headers.get('content-type') || '';
  if (contentType.includes('multipart/form-data')) {
    return POST_CREATE(request);
  }
  return POST_BULK(request);
}

async function POST_BULK(request) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) return NextResponse.json({ error: 'Akses ditolak' }, { status: 401 });
    const userData = await verifyToken(token);
    if (!userData || (userData.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Tidak berizin' }, { status: 403 });
    }
    const body = await request.json();
    const { action, ids, status } = body;
    if (action !== 'bulkUpdateStatus') {
      return NextResponse.json({ error: 'Aksi tidak dikenali' }, { status: 400 });
    }
    const valid = ['pending','diproses','selesai','ditolak'];
    if (!Array.isArray(ids) || ids.length === 0 || !valid.includes(status)) {
      return NextResponse.json({ error: 'Parameter tidak valid' }, { status: 400 });
    }

    // Audit log for each changed status
    const oldKeberatan = await prisma.keberatanInformasi.findMany({ where: { id: { in: ids } } });
    const result = await prisma.keberatanInformasi.updateMany({
      where: { id: { in: ids } },
      data: { status, updatedAt: new Date(), adminId: userData.id }
    });
    try {
      for (const k of oldKeberatan) {
        if (k.status !== status) {
          await prisma.auditLog.create({
            data: {
              entityType: 'keberatan',
              entityId: k.id,
              action: 'bulkUpdate',
              field: 'status',
              oldValue: k.status,
              newValue: status,
              userId: userData.id,
            }
          });
        }
      }
    } catch (e) { console.error('AuditLog error', e); }

    return NextResponse.json({ success: true, updatedCount: result.count });
  } catch (e) {
    console.error('Error bulk update keberatan:', e);
    return NextResponse.json({ error: 'Kesalahan server' }, { status: 500 });
  }
}

async function POST_CREATE(request) {
  // Original POST implementation moved here
  try {
    const formData = await request.formData();
    
    // Extract form fields
    const data = {
      tanggalPermohonan: new Date(formData.get('tanggalPermohonan')),
      kategoriPemohon: formData.get('kategoriPemohon'),
      nik: formData.get('nik'),
      namaSesuaiKtp: formData.get('namaSesuaiKtp'),
      alamatLengkapSesuaiKtp: formData.get('alamatLengkapSesuaiKtp'),
      alamatTinggalSaatIni: formData.get('alamatTinggalSaatIni'),
      nomorKontak: formData.get('nomorKontak'),
      alamatEmail: formData.get('alamatEmail'),
      pekerjaan: formData.get('pekerjaan'),
      topikKeberatan: formData.get('topikKeberatan'),
      maksudKeberatan: formData.get('maksudKeberatan'),
      alasanKeberatan: formData.get('alasanKeberatan'),
      pernyataanKeberatan: formData.get('pernyataanKeberatan') === 'true'
    };

    // Validate required fields
    const requiredFields = [
      'kategoriPemohon', 'nik', 'namaSesuaiKtp', 'alamatLengkapSesuaiKtp',
      'alamatTinggalSaatIni', 'nomorKontak', 'alamatEmail', 'pekerjaan',
      'topikKeberatan', 'maksudKeberatan', 'alasanKeberatan'
    ];

    for (const field of requiredFields) {
      if (!data[field] || data[field].toString().trim() === '') {
        return NextResponse.json(
          { error: `Field ${field} wajib diisi` },
          { status: 400 }
        );
      }
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.alamatEmail)) {
      return NextResponse.json(
        { error: 'Format email tidak valid' },
        { status: 400 }
      );
    }

    if (!/^\d{16}$/.test(data.nik)) {
      return NextResponse.json(
        { error: 'NIK harus 16 digit angka' },
        { status: 400 }
      );
    }

    if (!data.pernyataanKeberatan) {
      return NextResponse.json(
        { error: 'Anda harus menyetujui pernyataan' },
        { status: 400 }
      );
    }

    const id = await generateUniqueId();

    let salinanKtpPath = null;
    const salinanKtpFile = formData.get('salinanKtp');
    
    if (salinanKtpFile && salinanKtpFile.size > 0) {
      if (salinanKtpFile.size > 1024 * 1024) {
        return NextResponse.json(
          { error: 'Ukuran file maksimal 1MB' },
          { status: 400 }
        );
      }

      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(salinanKtpFile.type)) {
        return NextResponse.json(
          { error: 'Format file harus JPG, PNG, atau PDF' },
          { status: 400 }
        );
      }

      const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'keberatan');
      try {
        await mkdir(uploadDir, { recursive: true });
      } catch (error) {}

      const timestamp = Date.now();
      const fileExtension = path.extname(salinanKtpFile.name);
      const fileName = `salinan-ktp-${id}-${timestamp}${fileExtension}`;
      const filePath = path.join(uploadDir, fileName);

      const bytes = await salinanKtpFile.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(filePath, buffer);

      salinanKtpPath = `/uploads/keberatan/${fileName}`;
    } else {
      return NextResponse.json(
        { error: 'Salinan KTP wajib diupload' },
        { status: 400 }
      );
    }

    const keberatan = await prisma.keberatanInformasi.create({
      data: {
        id,
        ...data,
        salinanKtpPath,
        status: 'pending'
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Keberatan berhasil dikirim',
      id: keberatan.id
    });

  } catch (error) {
    console.error('Error creating keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}
