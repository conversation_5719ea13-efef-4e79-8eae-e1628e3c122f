"use client";
import React from 'react';
import { usePathname } from 'next/navigation';
import Footer from './Footer';

export default function FooterRouteGuard() {
  const pathname = usePathname();
  if (!pathname) return null;

  // Hide footer on all dashboard and admin routes
  if (
    pathname === '/dashboard' || pathname.startsWith('/dashboard/') ||
    pathname === '/admin' || pathname.startsWith('/admin/')
  ) {
    return null;
  }

  // Keep footer hidden on mobile globally
  return (
    <div className="hidden sm:block">
      <Footer />
    </div>
  );
}
