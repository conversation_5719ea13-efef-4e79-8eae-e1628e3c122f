// Debug script untuk memeriksa masalah autentikasi
// Jalankan dengan: node debug-auth.js

const fs = require('fs');
const path = require('path');

console.log('🔍 Debug Autentikasi PPID BPMP\n');

// 1. Periksa file-file penting
const filesToCheck = [
  'app/context/AuthContext.js',
  'app/hooks/useAuth.js',
  'app/api/auth/me/route.js',
  'app/api/auth/login/route.js',
  'app/lib/auth.js',
  'app/lib/prisma.js'
];

console.log('📁 Memeriksa file-file penting:');
filesToCheck.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`   ${exists ? '✅' : '❌'} ${file}`);
});

// 2. Periksa environment variables
console.log('\n🔐 Environment Variables:');
const requiredEnvs = ['JWT_SECRET', 'DATABASE_URL'];
requiredEnvs.forEach(env => {
  const exists = process.env[env] ? true : false;
  console.log(`   ${exists ? '✅' : '❌'} ${env}: ${exists ? 'Set' : 'Not set'}`);
});

// 3. Periksa import paths di AuthContext
console.log('\n📦 Memeriksa AuthContext:');
try {
  const authContextPath = 'app/context/AuthContext.js';
  if (fs.existsSync(authContextPath)) {
    const content = fs.readFileSync(authContextPath, 'utf8');
    
    // Periksa apakah menggunakan cookies
    const usesCookies = content.includes("credentials: 'include'");
    console.log(`   ${usesCookies ? '✅' : '❌'} Menggunakan cookies: ${usesCookies}`);
    
    // Periksa endpoint yang dipanggil
    const callsAuthMe = content.includes('/api/auth/me');
    console.log(`   ${callsAuthMe ? '✅' : '❌'} Memanggil /api/auth/me: ${callsAuthMe}`);
    
    // Periksa fungsi yang tersedia
    const hasFunctions = {
      login: content.includes('const login ='),
      logout: content.includes('const logout ='),
      checkAuthStatus: content.includes('const checkAuthStatus =')
    };
    
    Object.entries(hasFunctions).forEach(([func, exists]) => {
      console.log(`   ${exists ? '✅' : '❌'} Fungsi ${func}: ${exists}`);
    });
  }
} catch (error) {
  console.log(`   ❌ Error reading AuthContext: ${error.message}`);
}

// 4. Periksa API route /api/auth/me
console.log('\n🔌 Memeriksa API Route /api/auth/me:');
try {
  const apiPath = 'app/api/auth/me/route.js';
  if (fs.existsSync(apiPath)) {
    const content = fs.readFileSync(apiPath, 'utf8');
    
    // Periksa import paths
    const imports = {
      verifyToken: content.includes("from '../../../lib/auth'"),
      prisma: content.includes("from '../../../lib/prisma'")
    };
    
    Object.entries(imports).forEach(([imp, correct]) => {
      console.log(`   ${correct ? '✅' : '❌'} Import ${imp}: ${correct ? 'Correct' : 'Incorrect'}`);
    });
    
    // Periksa cookie handling
    const handlesCookies = content.includes('request.cookies.get');
    console.log(`   ${handlesCookies ? '✅' : '❌'} Handles cookies: ${handlesCookies}`);
    
    // Periksa Authorization header
    const handlesAuth = content.includes('authorization');
    console.log(`   ${handlesAuth ? '✅' : '❌'} Handles Authorization header: ${handlesAuth}`);
  }
} catch (error) {
  console.log(`   ❌ Error reading API route: ${error.message}`);
}

// 5. Periksa login API
console.log('\n🔑 Memeriksa API Route /api/auth/login:');
try {
  const loginPath = 'app/api/auth/login/route.js';
  if (fs.existsSync(loginPath)) {
    const content = fs.readFileSync(loginPath, 'utf8');
    
    // Periksa apakah set cookies
    const setsCookies = content.includes('response.cookies.set');
    console.log(`   ${setsCookies ? '✅' : '❌'} Sets cookies: ${setsCookies}`);
    
    // Periksa token names
    const tokenNames = {
      accessToken: content.includes("name: 'token'"),
      refreshToken: content.includes("name: 'refresh_token'")
    };
    
    Object.entries(tokenNames).forEach(([token, exists]) => {
      console.log(`   ${exists ? '✅' : '❌'} Sets ${token} cookie: ${exists}`);
    });
  }
} catch (error) {
  console.log(`   ❌ Error reading login route: ${error.message}`);
}

console.log('\n' + '='.repeat(50));
console.log('🎯 Kemungkinan Masalah:');
console.log('1. Pastikan JWT_SECRET sudah di-set di .env');
console.log('2. Pastikan database sudah running');
console.log('3. Cek browser cookies setelah login');
console.log('4. Cek Network tab di DevTools untuk request /api/auth/me');
console.log('5. Pastikan tidak ada konflik antara localStorage dan cookies');
console.log('='.repeat(50));
