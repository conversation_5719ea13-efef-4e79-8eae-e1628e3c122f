"use client";
import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';

export default function EditUrlModalPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id;
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    async function load() {
      try {
        const res = await fetch(`/api/urlmodals/${id}`);
        if (!res.ok) throw new Error('Gagal memuat data');
        const json = await res.json();
        setTitle(json.title || '');
        setContent(json.content || '');
      } catch (e) {
        setError(e.message || 'Gagal memuat');
      } finally {
        setLoading(false);
      }
    }
    if (id) load();
  }, [id]);

  async function submit(e) {
    e.preventDefault();
    setSaving(true);
    setError('');
    try {
      const res = await fetch(`/api/urlmodals/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title, content })
      });
      if (!res.ok) throw new Error('Gagal memperbarui');
      router.push('/dashboard/urlmodals');
    } catch (e) {
      setError(e.message || 'Gagal memperbarui');
    } finally {
      setSaving(false);
    }
  }

  if (loading) return <p>Memuat...</p>;

  return (
    <div className="max-w-3xl p-4 bg-white border rounded">
      <h1 className="mb-4 text-xl font-semibold">Edit Modal</h1>
      {error && <p className="mb-3 text-red-600">{error}</p>}
      <form onSubmit={submit} className="space-y-4">
        <div>
          <label className="block mb-1 text-sm font-medium">Judul</label>
          <input className="w-full p-2 border rounded" value={title} onChange={(e) => setTitle(e.target.value)} required />
        </div>
        <div>
          <label className="block mb-1 text-sm font-medium">Konten (HTML diizinkan)</label>
          <textarea className="w-full p-2 font-mono border rounded min-h-64" value={content} onChange={(e) => setContent(e.target.value)} required />
        </div>
        <div className="flex gap-2">
          <button disabled={saving} className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50">{saving ? 'Menyimpan...' : 'Simpan Perubahan'}</button>
          <button type="button" onClick={() => router.push('/dashboard/urlmodals')} className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">Batal</button>
        </div>
      </form>
    </div>
  );
}
