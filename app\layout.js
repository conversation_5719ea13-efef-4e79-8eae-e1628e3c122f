import { Lato } from 'next/font/google';
import './globals.css';
import ClientWrapper from './ClientWrapper';
import { AccessibilityProvider } from './context/AccessibilityContext';
import { OrganizationStructuredData, WebSiteStructuredData } from './components/StructuredData';
import AccessibilityControlsLazy from './components/AccessibilityControlsLazy';
import FooterRouteGuard from './components/FooterRouteGuard';

const lato = Lato({
  subsets: ['latin'],
  weight: ['400', '700'],
  style: ['normal', 'italic'],
  display: 'swap',
  preload: true,
  adjustFontFallback: true,
});

import { config, getCanonicalUrl, getProductionUrl } from '../lib/config';

const baseUrl = getProductionUrl();

export const metadata = {
  title: {
    default: config.app.name,
    template: `%s | ${config.app.name}`
  },
  description: config.app.description,
  keywords: [
    'PPID', 'BPMP', 'Kalimantan Timur', 'informasi publik', 'permohonan informasi', 
    'keberatan', 'transparansi', 'akuntabilitas', 'pemerintah daerah', 'keterbukaan informasi',
    'layanan publik', 'pendidikan', 'mutu pendidikan', 'kualitas pendidikan'
  ].join(', '),
  authors: [{ name: 'BPMP Provinsi Kalimantan Timur' }],
  creator: 'BPMP Provinsi Kalimantan Timur',
  publisher: 'BPMP Provinsi Kalimantan Timur',
  category: 'Government',
  classification: 'Public Service',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/',
    languages: {
      'id-ID': '/',
    }
  },
  openGraph: {
    title: config.app.name,
    description: config.app.description,
    url: baseUrl,
    siteName: config.app.name,
    locale: 'id_ID',
    type: 'website',
    images: [
      {
        url: `${baseUrl}/logo.png`,
        width: 800,
        height: 600,
        alt: config.app.name,
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: config.app.name,
    description: config.app.description,
    images: [`${baseUrl}/logo.png`],
    creator: '@bpmp_kaltim',
    site: '@bpmp_kaltim',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    other: {
      'msvalidate.01': process.env.BING_SITE_VERIFICATION,
      // 'yandex-verification': process.env.YANDEX_SITE_VERIFICATION,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#3b82f6' },
    ],
  },
  manifest: '/site.webmanifest',
  other: {
    'theme-color': '#3b82f6',
    'color-scheme': 'light',
  }
};

export default function RootLayout({ children }) {
  return (
    <html lang="id" dir="ltr">
      <body className={`${lato.className} min-h-screen antialiased bg-white text-gray-900`}>
        {/* Organization Structured Data */}
        <OrganizationStructuredData />
        {/* Website Structured Data */}
        <WebSiteStructuredData />
        
        <AccessibilityProvider>
          <ClientWrapper>
            {children}
          </ClientWrapper>
          <FooterRouteGuard />
          <AccessibilityControlsLazy />
        </AccessibilityProvider>
      </body>
    </html>
  );
}
