'use client';

import { useMemo, Suspense } from 'react';
import sanitizeHtml from 'sanitize-html';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

/**
 * Safe Content Renderer dengan sanitization untuk mencegah XSS
 * Khusus dioptimasi untuk konten dengan accordion (details/summary)
 */
export default function SafeContentRenderer({ content, className = '' }) {
  return (
    <Suspense fallback={
      <div className="p-4 italic text-center text-gray-500">Memuat konten…</div>
    }>
      <InnerSafeContentRenderer content={content} className={className} />
    </Suspense>
  );
}

function InnerSafeContentRenderer({ content, className = '' }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const sanitizedContent = useMemo(() => {
    if (!content) return '';

    // First, decode HTML entities if they exist
    let decodedContent = content;
    
    // Check if content contains encoded HTML entities
    if (content.includes('&lt;') || content.includes('&gt;') || content.includes('&amp;')) {
      // Manual decoding of common HTML entities
      decodedContent = content
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ');
        
      console.log('Decoded HTML entities in content');
    }

    const sanitizeOptions = {
      allowedTags: [
        // Basic HTML tags
        'p', 'br', 'strong', 'em', 'u', 'i', 'b',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li',
        'blockquote',
        'a',
        'div', 'span',
        // Images
        'img',
        
        // Accordion specific tags
        'details', 'summary',
        
        // Table tags (if needed)
        'table', 'thead', 'tbody', 'tr', 'td', 'th',
      ],
      
      allowedAttributes: {
        'a': ['href', 'title', 'target', 'rel', 'class'],
        'img': ['src', 'alt', 'title', 'width', 'height', 'loading', 'decoding', 'class'],
        'details': ['class', 'open'],
        'summary': ['class'],
        'div': ['class'],
        'span': ['class'],
        'p': ['class'],
        'h1': ['class'],
        'h2': ['class'],
        'h3': ['class'],
        'h4': ['class'],
        'h5': ['class'],
        'h6': ['class'],
        'ul': ['class'],
        'ol': ['class'],
        'li': ['class'],
        'blockquote': ['class'],
        'table': ['class'],
        'thead': ['class'],
        'tbody': ['class'],
        'tr': ['class'],
        'td': ['class'],
        'th': ['class'],
      },
      
      allowedClasses: {
        // Tailwind classes untuk accordion
        'details': [
          'border', 'rounded-md', 'p-4', 'my-3', 'mb-4', 'mt-4',
          'bg-gray-50', 'bg-blue-50', 'bg-green-50', 'bg-yellow-50',
          'border-gray-200', 'border-blue-200', 'border-green-200',
          'shadow-sm', 'shadow'
        ],
        'summary': [
          'cursor-pointer', 'font-semibold', 'font-medium', 'font-bold',
          'text-blue-600', 'text-green-600', 'text-gray-800', 'text-gray-900',
          'hover:text-blue-800', 'hover:text-green-800',
          'text-sm', 'text-base', 'text-lg',
          'mb-2', 'pb-2', 'border-b', 'border-gray-200'
        ],
        'div': [
          'mt-2', 'mt-3', 'mt-4', 'mb-2', 'mb-3', 'mb-4',
          'text-gray-700', 'text-gray-600', 'text-gray-800',
          'text-sm', 'text-base',
          'p-2', 'p-3', 'p-4',
          'pl-4', 'pr-4', 'pt-2', 'pb-2',
          'space-y-2', 'space-y-3',
          'prose', 'prose-sm',
          'text-center'
        ],
        'p': [
          'mb-2', 'mb-3', 'mb-4', 'mt-2', 'mt-3', 'mt-4',
          'text-gray-700', 'text-gray-600', 'text-gray-800',
          'text-sm', 'text-base', 'leading-none',
          'text-center'
        ],
        'h1': ['text-2xl', 'text-3xl', 'font-bold', 'font-semibold', 'mb-4', 'text-gray-900', 'text-center'],
        'h2': ['text-xl', 'text-2xl', 'font-bold', 'font-semibold', 'mb-3', 'text-gray-900', 'text-center'],
        'h3': ['text-lg', 'text-xl', 'font-bold', 'font-semibold', 'mb-3', 'text-gray-900', 'text-center'],
        'h4': ['text-base', 'text-lg', 'font-bold', 'font-semibold', 'mb-2', 'text-gray-900', 'text-center'],
        'ul': ['list-disc', 'list-inside', 'pl-4', 'space-y-1', 'space-y-2'],
        'ol': ['list-decimal', 'list-inside', 'pl-4', 'space-y-1', 'space-y-2'],
        'li': ['mb-1', 'text-gray-700'],
        'blockquote': [
          'border-l-4', 'border-gray-300', 'border-blue-300',
          'pl-4', 'py-2', 'italic', 'text-gray-600',
          'bg-gray-50', 'rounded-r'
        ],
        'strong': ['font-bold', 'font-semibold'],
        'em': ['italic'],
        'a': [
          'text-blue-600', 'hover:text-blue-800',
          // Removed 'underline' to avoid forced underline via utility class
          'hover:no-underline', 'transition-colors',
          'no-underline'
        ],
        'img': [
          'mx-auto', 'rounded', 'rounded-md',
          'w-full', 'h-auto', 'max-w-full',
          'object-cover', 'object-contain',
          'shadow', 'shadow-sm', 'shadow-md'
        ]
      },
      
      allowedSchemes: ['http', 'https', 'mailto'],
      
      // Transform functions untuk memastikan accordion berfungsi
      transformTags: {
        'details': (tagName, attribs) => {
          // Pastikan details memiliki class minimal
          const defaultClass = 'border rounded-md p-4 my-3';
          const existingClass = attribs.class || '';
          const finalClass = existingClass ? `${defaultClass} ${existingClass}` : defaultClass;
          
          return {
            tagName: 'details',
            attribs: {
              ...attribs,
              class: finalClass
            }
          };
        },
        
        'summary': (tagName, attribs) => {
          // Pastikan summary memiliki class minimal
          const defaultClass = 'cursor-pointer font-semibold text-blue-600';
          const existingClass = attribs.class || '';
          const finalClass = existingClass ? `${defaultClass} ${existingClass}` : defaultClass;
          
          return {
            tagName: 'summary',
            attribs: {
              ...attribs,
              class: finalClass
            }
          };
        }
      }
    };

    return sanitizeHtml(decodedContent, sanitizeOptions);
  }, [content]);

  if (!content) {
    return (
      <div className="p-4 italic text-center text-gray-500">
        Tidak ada konten untuk ditampilkan
      </div>
    );
  }

  function handleClick(e) {
    const anchor = e.target.closest && e.target.closest('a');
    if (!anchor) return;
    const hrefAttr = anchor.getAttribute('href');
    if (!hrefAttr) return;
    try {
      const url = new URL(hrefAttr, typeof window !== 'undefined' ? window.location.href : 'http://localhost');
      const hasModal = url.searchParams && url.searchParams.has('modal');
      if (!hasModal) return;
      e.preventDefault();
      // Satukan query baru dengan query saat ini, tetap di pathname yang sama
      const sp = new URLSearchParams(Array.from(searchParams.entries()));
      const modal = url.searchParams.get('modal');
      const modalData = url.searchParams.get('modalData');
      if (modal) sp.set('modal', modal);
      if (modalData) sp.set('modalData', modalData);
      const query = sp.toString();
      const newUrl = query ? `${pathname}?${query}` : pathname;
      router.replace(newUrl, { scroll: false });
    } catch {
      // Abaikan jika URL tidak valid
    }
  }

  return (
    <div 
      className={`unified-content max-w-none ${className}`}
      onClick={handleClick}
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  );
}

/**
 * Hook untuk preview content dalam editor
 */
export function useContentPreview(content) {
  return useMemo(() => {
    if (!content) return '';
    
    // Simple preview - strip some HTML but keep structure
    const previewOptions = {
      allowedTags: ['p', 'strong', 'em', 'details', 'summary'],
      allowedAttributes: {},
      textFilter: (text) => {
        // Limit text length for preview
        if (text.length > 150) {
          return text.substring(0, 150) + '...';
        }
        return text;
      }
    };
    
    return sanitizeHtml(content, previewOptions);
  }, [content]);
}
