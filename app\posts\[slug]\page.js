import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { prisma } from '../../../lib/prisma';
import Nav from '../../components/Nav';
import Sidebar from '../../components/Sidebar';
import SafeContentRenderer from '../../components/SafeContentRenderer';
import { ArticleStructuredData, BreadcrumbStructuredData } from '../../components/StructuredData';
import { formatDate } from '../../../lib/utils';

export async function generateMetadata(props) {
  const params = await props.params;
  const { slug } = params;
  
  const post = await prisma.post.findUnique({
    where: { slug },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      author: {
        select: {
          username: true,
        },
      },
    },
  });

  if (!post) {
    return {
      title: 'Post tidak ditemukan',
      description: 'Halaman yang Anda cari tidak ditemukan.',
    };
  }

  const baseUrl = process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN || 'http://localhost:3000';
  const postUrl = `${baseUrl}/posts/${post.slug}`;
  const keywords = post.tags?.map(t => t.tag.name).join(', ') || '';
  const publishedDate = post.publishedAt ? new Date(post.publishedAt).toISOString() : new Date(post.createdAt).toISOString();
  const modifiedDate = new Date(post.updatedAt).toISOString();

  return {
    title: post.title,
    description: post.excerpt || `${post.title} - Informasi publik dari BPMP Provinsi Kalimantan Timur`,
    keywords: keywords ? `${keywords}, PPID, BPMP, Kalimantan Timur, informasi publik` : 'PPID, BPMP, Kalimantan Timur, informasi publik',
    
    authors: [{ name: post.author?.username || 'BPMP Provinsi Kalimantan Timur' }],
    creator: post.author?.username || 'BPMP Provinsi Kalimantan Timur',
    publisher: 'BPMP Provinsi Kalimantan Timur',
    
    openGraph: {
      title: post.title,
      description: post.excerpt || `${post.title} - Informasi publik dari BPMP Provinsi Kalimantan Timur`,
      url: postUrl,
      type: 'article',
      locale: 'id_ID',
      siteName: 'PPID BPMP Prov. Kaltim',
      images: [{
        url: post.featuredImage || `${baseUrl}/logo.png`,
        width: 1200,
        height: 630,
        alt: post.title,
      }],
      publishedTime: publishedDate,
      modifiedTime: modifiedDate,
      authors: [post.author?.username || 'BPMP Provinsi Kalimantan Timur'],
      section: 'Informasi Publik',
    },
    
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || `${post.title} - Informasi publik dari BPMP Provinsi Kalimantan Timur`,
      images: [post.featuredImage || `${baseUrl}/logo.png`],
      creator: '@bpmpprovkaltim',
      site: '@bpmpprovkaltim',
    },
    
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    alternates: {
      canonical: postUrl,
    },
    
    other: {
      'article:author': post.author?.username || 'BPMP Provinsi Kalimantan Timur',
      'article:section': 'Informasi Publik',
      'article:published_time': publishedDate,
      'article:modified_time': modifiedDate,
      'article:tag': keywords,
    },
  };
}

async function getPost(slug) {
  const post = await prisma.post.findUnique({
    where: {
      slug,
      published: true,
    },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      author: {
        select: {
          username: true,
        },
      },
    },
  });
  
  return post;
}

export default async function PostPage(props) {
  const params = await props.params;
  const { slug } = params;
  const post = await getPost(slug);

  if (!post) {
    notFound();
  }

  // Breadcrumb data for structured data
  const breadcrumbItems = [
    { name: 'Beranda', url: '/' },
    { name: 'Informasi Publik', url: '/posts' },
    { name: post.title, url: `/posts/${post.slug}` }
  ];

  return (
    <>
      {/* Structured Data */}
      <ArticleStructuredData post={post} />
      <BreadcrumbStructuredData items={breadcrumbItems} />
      
      <div className="flex flex-col min-h-screen md:flex-row">
        <Nav />
        <div className="hidden md:block">
          <Sidebar theme="light" />
        </div>
        <article className="flex-1 w-full max-w-4xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
          {/* Breadcrumb Navigation */}
          <nav className="flex mb-6 text-sm" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/" className="text-gray-500 hover:text-primary-600">
                  Beranda
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <Link href="/posts" className="text-gray-500 hover:text-primary-600">
                    Informasi Publik
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <span className="text-gray-900 truncate max-w-xs">{post.title}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="mb-8">
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.map(({ tag }) => (
                <Link 
                  key={tag.id} 
                  href={`/posts/tag/${tag.slug}`}
                  prefetch={false}
                  className="px-3 py-1 text-sm font-medium rounded-full text-primary-700 bg-primary-100 hover:bg-primary-200"
                >
                  {tag.name}
                </Link>
              ))}
            </div>
            
            <h1 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
              {post.title}
            </h1>
            
            <div className="flex items-center gap-4 mb-6 text-sm text-gray-600">
              <span>Oleh: {post.author.username}</span>
              <span>•</span>
              <time dateTime={post.publishedAt}>{formatDate(post.publishedAt)}</time>
            </div>
            
            {post.featuredImage && (
              <div className="relative w-full h-64 mb-8 overflow-hidden rounded-lg md:h-96">
                <Image
                  src={post.featuredImage}
                  alt={post.title}
                  fill
                  className="object-cover"
                  priority
                  sizes="(max-width: 768px) 100vw, 800px"
                  quality={70}
                />
              </div>
            )}
          </div>
          
          <SafeContentRenderer 
            content={post.content}
            className="prose-primary"
          />
        </article>
      </div>
    </>
  );
}
