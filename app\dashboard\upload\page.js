'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Copy, ExternalLink, Plus, Search, FileText, Upload, CheckCircle, AlertCircle, Eye, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import toast from 'react-hot-toast';

const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-'; // Check for Invalid Date
    
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (err) {
    console.error('Error formatting date:', err);
    return '-';
  }
};

export default function UploadPage() {
  const router = useRouter();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('dokumen');
  const [isPublic, setIsPublic] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [fetchingFiles, setFetchingFiles] = useState(true);
  const [copyTooltip, setCopyTooltip] = useState({ visible: false, fileId: null });

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalFiles, setTotalFiles] = useState(0);
  const [pagination, setPagination] = useState({});

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // Tipe file yang diizinkan
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  const fileExtensions = ['.pdf', '.jpeg', '.jpg', '.png'];

  // Fetch uploaded files with pagination and search
  const fetchFiles = useCallback(async (page, search, category, status) => {
    try {
      setFetchingFiles(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: String(page ?? 1),
        limit: '10'
      });

      if (search) params.append('search', search);
      if (category) params.append('category', category);
      if (status) params.append('isPublic', status);

      const response = await fetch(`/api/files?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        setUploadedFiles(data.files || []);
        setPagination(data.pagination || {});
        // Jangan setCurrentPage dari respons untuk menghindari loop
        setTotalPages(data.pagination?.totalPages || 1);
        setTotalFiles(data.pagination?.totalFiles || 0);
      }
    } catch (err) {
      console.error('Error fetching files:', err);
    } finally {
      setFetchingFiles(false);
    }
  }, []);

  // Muat ulang data saat halaman, pencarian, filter, atau sukses upload berubah
  useEffect(() => {
    fetchFiles(currentPage, searchQuery, selectedCategory, selectedStatus);
  }, [currentPage, searchQuery, selectedCategory, selectedStatus, success, fetchFiles]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(searchTerm);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Fetch files when search query, category, or status changes
  useEffect(() => {
    if (searchQuery !== undefined || selectedCategory !== undefined || selectedStatus !== undefined) {
      fetchFiles(1, searchQuery, selectedCategory, selectedStatus);
    }
  }, [searchQuery, selectedCategory, selectedStatus, fetchFiles]);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    validateAndSetFile(selectedFile);
  };

  const validateAndSetFile = (selectedFile) => {
    setError('');
    
    if (!selectedFile) return;
    
    if (!allowedTypes.includes(selectedFile.type)) {
      setError(`Tipe file tidak diizinkan. Hanya file ${fileExtensions.join(', ')} yang diperbolehkan.`);
      return;
    }
    
    if (selectedFile.size > 15 * 1024 * 1024) {
      setError('Ukuran file terlalu besar. Maksimal 15MB.');
      return;
    }
    
    setFile(selectedFile);
    setFileName(selectedFile.name);
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file) {
      toast.error('Silakan pilih file terlebih dahulu.');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileName', fileName);
      formData.append('description', description);
      formData.append('category', category);
      formData.append('isPublic', isPublic);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan saat mengunggah file');
      }
      
      toast.success('File berhasil diunggah!');
      setFile(null);
      setFileName('');
      setDescription('');
      setShowUploadForm(false);
      setSuccess(true);
      
      // Refresh data to show new file -> cukup ubah halaman, efek akan fetch
      setCurrentPage(1);
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
      
    } catch (err) {
      console.error('Upload error:', err);
      setError(err.message || 'Terjadi kesalahan saat mengunggah file');
      toast.error(err.message || 'Terjadi kesalahan saat mengunggah file');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFile = async (fileId) => {
    if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {
      try {
        setLoading(true);
        const response = await fetch(`/api/files/${fileId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          toast.success('File berhasil dihapus!');
          // Refresh current page data
          fetchFiles(currentPage, searchQuery, selectedCategory, selectedStatus);
        } else {
          throw new Error('Gagal menghapus file');
        }
      } catch (err) {
        toast.error(err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const copyToClipboard = async (text, elementId, type = 'path') => {
    try {
      await navigator.clipboard.writeText(text);
      
      // Show tooltip
      setCopyTooltip({ visible: true, fileId: elementId });
      
      // Success message based on type
      const message = type === 'path' ? 'Path berhasil disalin!' : 'URL berhasil disalin!';
      toast.success(message);
      
      // Hide tooltip after 2 seconds
      setTimeout(() => {
        setCopyTooltip({ visible: false, fileId: null });
      }, 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const getFullUrl = (path) => {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
    return `${baseUrl}${path}`;
  };

  return (
    <div className="flex-1">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Media</h1>
            <p className="mt-1 text-sm text-gray-600">
              Kelola file media dan salin path untuk digunakan dalam post
            </p>
          </div>
        </div>
      </header>

      <main className="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {/* WordPress-style Media Library */}
        <div className="bg-white rounded-lg shadow">
          {/* Toolbar */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowUploadForm(!showUploadForm)}
                className="flex items-center px-3 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Media
              </button>
            </div>
            
            <div className="flex items-center space-x-3">
              <input
                type="text"
                placeholder="Cari media..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Semua Kategori</option>
                <option value="dokumen">Dokumen</option>
                <option value="gambar">Gambar</option>
                <option value="laporan">Laporan</option>
                <option value="lainnya">Lainnya</option>
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Semua Status</option>
                <option value="true">Publik</option>
                <option value="false">Private</option>
              </select>
            </div>
          </div>
          
          {/* Success/Error Messages */}
          {success && (
            <div className="flex items-center p-4 m-4 text-green-700 bg-green-100 rounded-md">
              <CheckCircle className="mr-2" size={20} />
              <span>File berhasil diunggah!</span>
            </div>
          )}
          
          {error && (
            <div className="flex items-center p-4 m-4 text-red-700 bg-red-100 rounded-md">
              <AlertCircle className="mr-2" size={20} />
              <span>{error}</span>
            </div>
          )}
          
          {/* Upload Form (Collapsible) */}
          {showUploadForm && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="p-4 border-b"
            >
              <form onSubmit={handleSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 text-center col-span-2 ${
                    dragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    id="file-upload"
                    onChange={handleFileChange}
                    accept=".pdf,.jpeg,.jpg,.png"
                    className="hidden"
                  />
                  
                  {file ? (
                    <div className="flex flex-col items-center">
                      <FileText size={40} className="mb-2 text-primary-500" />
                      <p className="mb-1 font-medium">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <button
                        type="button"
                        onClick={() => {
                          setFile(null);
                          setFileName('');
                        }}
                        className="mt-2 text-sm text-red-600 hover:text-red-800"
                      >
                        Hapus
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Upload size={40} className="mb-2 text-gray-400" />
                      <p className="mb-2 text-sm text-gray-500">
                        Drag & drop file di sini, atau
                      </p>
                      <label
                        htmlFor="file-upload"
                        className="px-4 py-2 text-sm font-medium text-white rounded-md cursor-pointer bg-primary-600 hover:bg-primary-700"
                      >
                        Pilih File
                      </label>
                      <p className="mt-2 text-xs text-gray-500">
                        Format yang didukung: PDF, JPEG, PNG (Maks. 15MB)
                      </p>
                    </div>
                  )}
                </div>
                
                <div>
                  <label htmlFor="fileName" className="block mb-1 text-sm font-medium text-gray-700">
                    Nama File
                  </label>
                  <input
                    type="text"
                    id="fileName"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="category" className="block mb-1 text-sm font-medium text-gray-700">
                    Kategori
                  </label>
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="dokumen">Dokumen</option>
                    <option value="gambar">Gambar</option>
                    <option value="laporan">Laporan</option>
                    <option value="lainnya">Lainnya</option>
                  </select>
                </div>
                
                <div className="col-span-2">
                  <label htmlFor="description" className="block mb-1 text-sm font-medium text-gray-700">
                    Deskripsi
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows="2"
                  />
                </div>
                
                <div className="col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isPublic}
                      onChange={(e) => setIsPublic(e.target.checked)}
                      className="w-4 h-4 border-gray-300 rounded text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">File dapat diakses publik</span>
                  </label>
                </div>
                
                <div className="flex justify-end col-span-2 space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowUploadForm(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    disabled={loading || !file}
                    className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                      loading || !file
                        ? 'bg-primary-400 cursor-not-allowed'
                        : 'bg-primary-600 hover:bg-primary-700'
                    }`}
                  >
                    {loading ? 'Mengunggah...' : 'Upload'}
                  </button>
                </div>
              </form>
            </motion.div>
          )}

          {/* Info Box */}
          {uploadedFiles.length > 0 && (
            <div className="p-4 m-4 border border-blue-200 rounded-lg bg-blue-50">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Cara Menggunakan File dalam Konten
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="space-y-1 list-disc list-inside">
                      <li><strong>Path File:</strong> Gunakan untuk markdown atau HTML dalam post/artikel</li>
                      <li><strong>URL Lengkap:</strong> Gunakan untuk akses langsung atau share ke pengguna</li>
                      <li><strong>Contoh Markdown:</strong> <code className="px-1 bg-blue-100 rounded">![Alt text](/uploads/kategori/file.jpg)</code></li>
                      <li><strong>Contoh HTML:</strong> <code className="px-1 bg-blue-100 rounded">&lt;img src="/uploads/kategori/file.jpg" alt="Deskripsi"&gt;</code></li>
                      <li><strong>File PDF:</strong> <code className="px-1 bg-blue-100 rounded">[Download PDF](/uploads/dokumen/file.pdf)</code></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Files Table */}
          {fetchingFiles ? (
            <div className="flex justify-center py-8">
              <div className="w-8 h-8 border-4 rounded-full border-primary-500 border-t-transparent animate-spin"></div>
            </div>
          ) : uploadedFiles.length === 0 ? (
            <div className="py-12 text-center text-gray-500">
              <FileText size={48} className="mx-auto mb-2 text-gray-400" />
              <p className="text-lg">Belum ada file yang diupload</p>
              <p className="mt-2 text-sm">Klik "Tambah Media" untuk mengunggah file baru</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">File</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Path/Link</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Kategori</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Tanggal</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Aksi</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {uploadedFiles.map((file) => (
                    <tr key={file.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="flex-shrink-0 w-5 h-5 mr-3 text-gray-400" />
                          <div className="max-w-xs truncate">
                            <div className="font-medium text-gray-900">{file.originalName}</div>
                            <div className="text-sm text-gray-500 truncate">{file.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          {/* Path untuk markdown/HTML */}
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium text-gray-500">Path (untuk markdown):</div>
                              <div className="max-w-xs font-mono text-sm text-gray-900 truncate" title={file.path}>
                                {file.path}
                              </div>
                            </div>
                            <button
                              onClick={() => copyToClipboard(file.path, `path-${file.id}`, 'path')}
                              className="relative p-1 text-gray-400 hover:text-gray-600"
                              title="Salin path"
                            >
                              <Copy className="w-4 h-4" />
                              {copyTooltip.visible && copyTooltip.fileId === `path-${file.id}` && (
                                <div className="absolute px-2 py-1 text-xs text-white transform -translate-x-1/2 bg-black rounded -top-8 left-1/2">
                                  Tersalin!
                                </div>
                              )}
                            </button>
                          </div>
                          
                          {/* Full URL untuk akses langsung */}
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 min-w-0">
                              <div className="text-xs font-medium text-gray-500">URL lengkap:</div>
                              <div className="max-w-xs font-mono text-sm text-blue-600 truncate" title={getFullUrl(file.path)}>
                                {getFullUrl(file.path)}
                              </div>
                            </div>
                            <button
                              onClick={() => copyToClipboard(getFullUrl(file.path), `url-${file.id}`, 'url')}
                              className="relative p-1 text-gray-400 hover:text-gray-600"
                              title="Salin URL lengkap"
                            >
                              <ExternalLink className="w-4 h-4" />
                              {copyTooltip.visible && copyTooltip.fileId === `url-${file.id}` && (
                                <div className="absolute px-2 py-1 text-xs text-white transform -translate-x-1/2 bg-black rounded -top-8 left-1/2">
                                  Tersalin!
                                </div>
                              )}
                            </button>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">
                          {file.category}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                          file.isPublic 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {file.isPublic ? 'Publik' : 'Private'}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(file.uploadedAt)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                        <div className="flex space-x-3">
                          <button
                            onClick={() => window.open(file.path, '_blank')}
                            className="text-blue-600 hover:text-blue-900"
                            title="Lihat file"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus file"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Pagination */}
          {totalFiles > 0 && (
            <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
              <div className="flex justify-between flex-1 sm:hidden">
                <button
                  onClick={() => {
                    if (pagination.hasPrevPage) {
                      const newPage = currentPage - 1;
                      setCurrentPage(newPage);
                    }
                  }}
                  disabled={!pagination.hasPrevPage}
                  className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 rounded-md ${
                    pagination.hasPrevPage
                      ? 'text-gray-700 bg-white hover:bg-gray-50'
                      : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => {
                    if (pagination.hasNextPage) {
                      const newPage = currentPage + 1;
                      setCurrentPage(newPage);
                    }
                  }}
                  disabled={!pagination.hasNextPage}
                  className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 rounded-md ${
                    pagination.hasNextPage
                      ? 'text-gray-700 bg-white hover:bg-gray-50'
                      : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{((currentPage - 1) * 10) + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * 10, totalFiles)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{totalFiles}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <button
                      onClick={() => {
                        if (pagination.hasPrevPage) {
                          const newPage = currentPage - 1;
                          setCurrentPage(newPage);
                        }
                      }}
                      disabled={!pagination.hasPrevPage}
                      className={`relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-l-md focus:z-10 ${
                        pagination.hasPrevPage
                          ? 'text-gray-500 bg-white hover:bg-gray-50'
                          : 'text-gray-300 bg-gray-100 cursor-not-allowed'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="w-5 h-5" aria-hidden="true" />
                    </button>

                    {/* Page Numbers */}
                    {(() => {
                      const pages = [];
                      const maxVisiblePages = 5;
                      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                      // Adjust start page if we're near the end
                      if (endPage - startPage + 1 < maxVisiblePages) {
                        startPage = Math.max(1, endPage - maxVisiblePages + 1);
                      }

                      // Add first page and ellipsis if needed
                      if (startPage > 1) {
                        pages.push(
                          <button
                            key={1}
                            onClick={() => {
                              setCurrentPage(1);
                            }}
                            className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10"
                          >
                            1
                          </button>
                        );

                        if (startPage > 2) {
                          pages.push(
                            <span key="ellipsis1" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
                              ...
                            </span>
                          );
                        }
                      }

                      // Add visible page numbers
                      for (let i = startPage; i <= endPage; i++) {
                        pages.push(
                          <button
                            key={i}
                            onClick={() => {
                              setCurrentPage(i);
                            }}
                            className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 focus:z-10 ${
                              i === currentPage
                                ? 'bg-primary-50 border-primary-500 text-primary-600'
                                : 'text-gray-700 bg-white hover:bg-gray-50'
                            }`}
                          >
                            {i}
                          </button>
                        );
                      }

                      // Add ellipsis and last page if needed
                      if (endPage < totalPages) {
                        if (endPage < totalPages - 1) {
                          pages.push(
                            <span key="ellipsis2" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
                              ...
                            </span>
                          );
                        }

                        pages.push(
                          <button
                            key={totalPages}
                            onClick={() => {
                              setCurrentPage(totalPages);
                            }}
                            className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10"
                          >
                            {totalPages}
                          </button>
                        );
                      }

                      return pages;
                    })()}

                    <button
                      onClick={() => {
                        if (pagination.hasNextPage) {
                          const newPage = currentPage + 1;
                          setCurrentPage(newPage);
                        }
                      }}
                      disabled={!pagination.hasNextPage}
                      className={`relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-r-md focus:z-10 ${
                        pagination.hasNextPage
                          ? 'text-gray-500 bg-white hover:bg-gray-50'
                          : 'text-gray-300 bg-gray-100 cursor-not-allowed'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="w-5 h-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}



