"use client";
import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

import { 
  ExclamationTriangleIcon, 
  DocumentTextIcon, 
  ArrowDownTrayIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import toast from 'react-hot-toast';

export default function LogsPage() {
  const router = useRouter();
  const [logFiles, setLogFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState('');
  const [loadingContent, setLoadingContent] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const refreshTimerRef = useRef(null);

  useEffect(() => {
    fetchLogFiles();
  }, []);

  const fetchLogFiles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/logs');
      
      if (!response.ok) {
        throw new Error('Failed to fetch log files');
      }
      
      const data = await response.json();
      setLogFiles(data.files || []);
    } catch (error) {
      console.error('Error fetching log files:', error);
      toast.error('Gagal memuat daftar file log');
    } finally {
      setLoading(false);
    }
  };

  const fetchLogContent = async (filename) => {
    try {
      setLoadingContent(true);
      // Use tail endpoint for responsiveness
      const response = await fetch(`/api/logs/${filename}/tail?lines=500`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch log content');
      }
      
      const data = await response.json();
      setFileContent(data.content);
      setSelectedFile(filename);
    } catch (error) {
      console.error('Error fetching log content:', error);
      toast.error('Gagal memuat isi file log');
    } finally {
      setLoadingContent(false);
    }
  };

  // refresh helper used by auto refresh & manual refresh
  const refreshSelectedFile = useCallback(async () => {
    if (!selectedFile) return;
    try {
      const response = await fetch(`/api/logs/${selectedFile}/tail?lines=500`);
      if (!response.ok) return;
      const data = await response.json();
      setFileContent(data.content);
    } catch {}
  }, [selectedFile]);

  useEffect(() => {
    // handle auto refresh when a file is selected
    if (autoRefresh && selectedFile) {
      // initial refresh without waiting the first interval
      refreshSelectedFile();
      refreshTimerRef.current = setInterval(() => {
        refreshSelectedFile();
      }, 5000);
    }
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
    };
  }, [autoRefresh, selectedFile, refreshSelectedFile]);

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('user');
    router.push('/login');
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
    else return (bytes / 1048576).toFixed(2) + ' MB';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'dd MMMM yyyy', { locale: id });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'HH:mm:ss', { locale: id });
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
     
      
      <div className="flex-1">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="flex items-center justify-between px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
            <h1 className="text-2xl font-bold text-gray-900">Log Sistem</h1>
            <div className="flex items-center gap-3">
              <button
                onClick={refreshSelectedFile}
                disabled={!selectedFile}
                className="px-3 py-1 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                title={selectedFile ? `Segarkan ${selectedFile}` : 'Pilih file log terlebih dahulu'}
              >
                Lihat terbaru
              </button>
              <label className="inline-flex items-center gap-2 text-sm text-gray-700 select-none">
                <input
                  type="checkbox"
                  className="w-4 h-4"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  disabled={!selectedFile}
                />
                Auto-refresh 5s
              </label>
            </div>
          </div>
        </header>
        
        <main className="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Daftar file log */}
            <div className="overflow-hidden bg-white rounded-lg shadow">
              <div className="px-4 py-5 sm:px-6">
                <h2 className="text-lg font-medium text-gray-900">File Log</h2>
                <p className="mt-1 text-sm text-gray-500">Daftar file log sistem</p>
              </div>
              
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="w-8 h-8 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
                </div>
              ) : (
                <div className="overflow-y-auto max-h-[calc(100vh-250px)]">
                  <ul className="divide-y divide-gray-200">
                    {logFiles.length === 0 ? (
                      <li className="px-4 py-5 text-center text-gray-500">
                        Belum ada file log
                      </li>
                    ) : (
                      logFiles.map((file) => (
                        <motion.li 
                          key={file.name}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          whileHover={{ backgroundColor: 'rgba(243, 244, 246, 0.5)' }}
                          className={`px-4 py-4 cursor-pointer ${selectedFile === file.name ? 'bg-blue-50' : ''}`}
                          onClick={() => fetchLogContent(file.name)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <DocumentTextIcon className="w-5 h-5 mr-3 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">{file.name}</p>
                                <div className="flex items-center mt-1 text-xs text-gray-500">
                                  <span className="flex items-center mr-3">
                                    <CalendarIcon className="w-3 h-3 mr-1" />
                                    {formatDate(file.modified)}
                                  </span>
                                  <span className="flex items-center">
                                    <ClockIcon className="w-3 h-3 mr-1" />
                                    {formatTime(file.modified)}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                          </div>
                        </motion.li>
                      ))
                    )}
                  </ul>
                </div>
              )}
            </div>
            
            {/* Isi file log */}
            <div className="overflow-hidden bg-white rounded-lg shadow lg:col-span-2">
              <div className="px-4 py-5 sm:px-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-gray-900">
                    {selectedFile ? selectedFile : 'Isi Log'}
                  </h2>
                  {selectedFile && (
                    <a 
                      href={`/api/logs/${selectedFile}`}
                      download={selectedFile}
                      className="flex items-center px-3 py-1 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    >
                      <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                      Download
                    </a>
                  )}
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  {selectedFile ? 'Isi file log yang dipilih' : 'Pilih file log untuk melihat isinya'}
                </p>
              </div>
              
              <div className="px-4 py-5 border-t border-gray-200 sm:px-6">
                {loadingContent ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="w-8 h-8 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
                  </div>
                ) : selectedFile ? (
                  <pre className="p-4 overflow-x-auto text-sm text-gray-800 bg-gray-50 rounded-md max-h-[calc(100vh-250px)]">
                    {fileContent || 'File kosong'}
                  </pre>
                ) : (
                  <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                    <ExclamationTriangleIcon className="w-12 h-12 mb-4" />
                    <p>Pilih file log dari daftar di sebelah kiri</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}