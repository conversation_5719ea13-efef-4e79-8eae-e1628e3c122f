const fs = require('fs');
const path = require('path');

console.log('===============================================');
console.log('    DEMO FILES ORGANIZATION - COMPLETE');
console.log('===============================================');
console.log('');

// Check demo files location
console.log('[1/3] Checking demo files location...');
const demoDir = path.join(__dirname);
const files = fs.readdirSync(demoDir);

const htmlFiles = files.filter(file => file.endsWith('.html'));
const jsFiles = files.filter(file => file.endsWith('.js') && file.startsWith('test-'));
const folders = files.filter(file => {
  const filePath = path.join(demoDir, file);
  return fs.statSync(filePath).isDirectory() && file !== '.';
});

console.log(`✅ Found ${htmlFiles.length} HTML demo files:`);
htmlFiles.forEach(file => {
  const stats = fs.statSync(path.join(demoDir, file));
  const size = stats.size;
  console.log(`   - ${file} (${size} bytes)`);
});

console.log(`✅ Found ${jsFiles.length} JavaScript test files:`);
jsFiles.forEach(file => {
  const stats = fs.statSync(path.join(demoDir, file));
  const size = stats.size;
  console.log(`   - ${file} (${size} bytes)`);
});

console.log(`✅ Found ${folders.length} demo component folders:`);
folders.forEach(folder => {
  console.log(`   - ${folder}/`);
});
console.log('');

// Verify root directory cleanup
console.log('[2/3] Verifying root directory cleanup...');
const rootDir = path.join(__dirname, '..');
const rootFiles = fs.readdirSync(rootDir);
const remainingDemo = rootFiles.filter(file => 
  (file.includes('demo') || file.includes('test') || file.includes('example')) && 
  (file.endsWith('.html') || file.endsWith('.js'))
);

if (remainingDemo.length === 0) {
  console.log('✅ Root directory clean - no demo/test files found');
} else {
  console.log('❌ Some demo/test files still in root directory:');
  remainingDemo.forEach(file => console.log(`   - ${file}`));
}
console.log('');

// Show organized structure
console.log('[3/3] Showing demo files categorization...');
console.log('');
console.log('🎭 Demo Files:');
console.log('   - accordion-feature-demo.html');
console.log('   - ckeditor-tab-demo.html');
console.log('');
console.log('🧪 Test Files:');
console.log('   - test-accordion-rendering.html');
console.log('   - test-button-types.html');
console.log('   - test-decode.js');
console.log('   - test-decode-simple.js');
console.log('');
console.log('📝 Example Files:');
console.log('   - accordion-hijau-example.html');
console.log('');
console.log('🔧 Test Components:');
console.log('   - test-accordion/ (Next.js component)');
console.log('');

console.log('===============================================');
console.log('✅ DEMO FILES ORGANIZATION COMPLETE!');
console.log('===============================================');
console.log('');
console.log('📁 All demo/test files moved to: demo/');
console.log(`📋 Total files organized: ${htmlFiles.length + jsFiles.length} files + ${folders.length} folders`);
console.log('📖 Documentation: demo/README.md');
console.log('');
console.log('🎯 Categories:');
console.log('   - Demo: 2 files');
console.log('   - Test: 4 files');
console.log('   - Example: 1 file');
console.log('   - Components: 1 folder');
console.log('');
console.log('🚀 Usage: start demo/file-name.html');
console.log('');
