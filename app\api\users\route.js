import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../lib/auth';
import { hash } from 'bcryptjs';

// GET /api/users - Mendapatkan semua pengguna
export async function GET() {
  try {
    // Auth required; only admin can list all users
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = await verifyToken(token);
    if (!currentUser || String(currentUser.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Normalize roles to lowercase for consistency
    const normalized = users.map(u => ({ ...u, role: u.role?.toLowerCase?.() || u.role }));
    return NextResponse.json({ users: normalized });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data pengguna' },
      { status: 500 }
    );
  }
}

// POST /api/users - Membuat pengguna baru
export async function POST(request) {
  try {
    // Only admin can create users
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const currentUser = await verifyToken(token);
    if (!currentUser || String(currentUser.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await request.json();
    // Validate input
    if (!data.username || !data.email || !data.password) {
      return NextResponse.json(
        { error: 'Username, email, dan password diperlukan' },
        { status: 400 }
      );
    }
    if (!String(data.email).includes('@')) {
      return NextResponse.json(
        { error: 'Email tidak valid' },
        { status: 400 }
      );
    }

    // Check duplicates
    const existingUser = await prisma.user.findFirst({
      where: { OR: [{ email: data.email }, { username: data.username }] },
      select: { id: true },
    });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email atau username sudah digunakan' },
        { status: 400 }
      );
    }

    // Hash password and normalize role to enum casing
    const passwordHash = await hash(data.password, 12);
    const normalizedRole = String(data.role || 'user').toLowerCase() === 'admin' ? 'ADMIN' : 'USER';

    const newUser = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        passwordHash,
        salt: 'manual_seed_' + Date.now(),
        role: normalizedRole,
      },
      select: { id: true, username: true, email: true, role: true, createdAt: true, updatedAt: true },
    });

    // Lowercase role in response for UI consistency
    return NextResponse.json({ user: { ...newUser, role: newUser.role.toLowerCase() }, success: true }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat pengguna' },
      { status: 500 }
    );
  }
}


