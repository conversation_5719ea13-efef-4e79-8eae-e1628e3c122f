// cardData.js
import { 
    DocumentTextIcon, 
    ClipboardDocumentListIcon, 
    DocumentDuplicateIcon, 
    QuestionMarkCircleIcon, 
    ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';

export const cards = [
    {
        title: 'Maklumat Pelayanan',
        description: 'Lihat maklumat pelayanan kami.',
        icon: <DocumentTextIcon className="w-6 h-6" />,
        action: 'link',
        link: '/maklumat.pdf',
        category: 'Dokumen'
    },
    {
        title: 'Formulir Permohonan',
        description: 'Isi formulir permohonan informasi secara online.',
        icon: <ClipboardDocumentListIcon className="w-6 h-6" />,
        action: 'link',
        link: '/permohonan',
        category: 'Formulir'
    },
    {
        title: 'Formulir Keberatan',
        description: 'Isi formulir keberatan atas permohonan informasi publik secara online.',
        icon: <DocumentDuplicateIcon className="w-6 h-6" />,
        action: 'link',
        link: '/keberatan',
        category: 'Formulir'
    },
    {
        title: 'FAQ',
        description: 'Pertanyaan yang sering diajukan.',
        icon: <QuestionMarkCircleIcon className="w-6 h-6" />,
        action: 'modal',
        modalContent: {
            title: 'Pertanyaan yang Sering Diajukan',
            content: 'Konten FAQ akan ditampilkan di sini.'
        },
        category: 'Informasi'
    },
    {
        title: 'Kontak Kami',
        description: 'Hubungi kami untuk informasi lebih lanjut.',
        icon: <ChatBubbleLeftRightIcon className="w-6 h-6" />,
        action: 'modal',
        modalContent: {
            title: 'Kontak Kami',
            content: 'Informasi kontak akan ditampilkan di sini.'
        },
        category: 'Informasi'
    }
]
