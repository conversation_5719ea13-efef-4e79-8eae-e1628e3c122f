# PPID BPMP - Deployment Guide (Non-Docker)

## 🚀 Overview
Panduan lengkap untuk deploy aplikasi PPID BPMP tanpa menggunakan Docker, menggunakan standalone build Next.js.

## 📋 Prerequisites
- **Node.js** v18.17.0 atau lebih baru
- **npm** atau **yarn**
- **MySQL Database** (sudah running)
- **PM2** (opsional, tapi direkomendasikan)

## 🏗️ Build Process

### 1. Quick Build
```bash
# Build aplikasi dengan semua dependencies
npm run build:standalone
```

### 2. Manual Build Step-by-Step
```bash
# Install dependencies
npm ci

# Generate Prisma client
npx prisma generate

# Build aplikasi
npm run build
```

## 🌐 Deployment Options

### Option 1: PM2 (Recommended untuk Production)

#### Install PM2 Global
```bash
npm install -g pm2
```

#### Deploy dengan PM2
```bash
# Build dan start dengan PM2
npm run deploy:pm2

# Restart aplikasi
npm run deploy:pm2:restart

# Stop aplikasi
npm run deploy:pm2:stop

# Monitor aplikasi
pm2 monit

# Logs
pm2 logs ppid-bpmp
```

#### PM2 Commands
```bash
# List running apps
pm2 list

# Restart app
pm2 restart ppid-bpmp

# Stop app
pm2 stop ppid-bpmp

# Delete app
pm2 delete ppid-bpmp

# Save PM2 configuration
pm2 save

# Startup script (auto-start on boot)
pm2 startup
```

### Option 2: Direct Node.js

#### Development/Testing
```bash
npm run deploy:vps
```

#### Manual Start
```bash
cd .next/standalone
node server.js
```

### Option 3: Using npm start
```bash
npm run build
npm start
```

## 🗄️ Database Setup

### 1. Environment Variables
Pastikan file `.env` sudah dikonfigurasi:
```env
DATABASE_URL="mysql://username:password@localhost:3306/database_name"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://your-domain.com"
```

### 2. Database Migration
```bash
# Run migrations
npx prisma migrate deploy

# Seed database (optional)
npx prisma db seed
```

## 🌍 Server Configuration

### 1. Environment Variables untuk Production
```env
NODE_ENV=production
PORT=3000
DATABASE_URL="mysql://username:password@localhost:3306/ppid_production"
NEXTAUTH_SECRET="production-secret-key"
NEXTAUTH_URL="https://ppid-bpmp-kaltim.go.id"
```

### 2. Nginx Configuration (Optional)
```nginx
server {
    listen 80;
    server_name ppid-bpmp-kaltim.go.id;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📁 File Structure Setelah Build

```
project-root/
├── .next/
│   ├── standalone/          # ← Main standalone build
│   │   ├── server.js       # ← Entry point
│   │   ├── package.json
│   │   └── ...
│   └── static/             # ← Static assets
├── public/                 # ← Public files
├── logs/                   # ← PM2 logs
├── ecosystem.config.json   # ← PM2 configuration
└── ...
```

## 🔧 Troubleshooting

### Common Issues:

1. **Port Already in Use**
   ```bash
   # Change port
   PORT=3001 node server.js
   ```

2. **Database Connection Failed**
   ```bash
   # Test database connection
   npx prisma db pull
   ```

3. **Missing Static Files**
   ```bash
   # Copy static files
   cp -r .next/static .next/standalone/.next/
   cp -r public .next/standalone/
   ```

4. **Permission Issues (Linux/Mac)**
   ```bash
   chmod +x deploy-standalone.sh
   ./deploy-standalone.sh
   ```

## 📊 Monitoring

### PM2 Monitoring
```bash
# Real-time monitoring
pm2 monit

# CPU and Memory usage
pm2 show ppid-bpmp

# Logs
pm2 logs ppid-bpmp --lines 100
```

### Health Check
Akses: `http://your-domain:3000/api/health` (jika endpoint tersedia)

## 🔒 Security Considerations

1. **Environment Variables**: Jangan commit file `.env` ke repository
2. **Database**: Gunakan strong password dan secure connection
3. **SSL/HTTPS**: Setup SSL certificate untuk production
4. **Firewall**: Batasi akses ke port yang diperlukan saja
5. **Updates**: Regular update dependencies

## 🚀 Quick Start Commands

```bash
# Windows
deploy-standalone.bat

# Linux/Mac
chmod +x deploy-standalone.sh
./deploy-standalone.sh

# Then choose deployment method:
npm run deploy:pm2        # PM2 (recommended)
npm run deploy:vps        # Direct Node.js
```

## 📞 Support

Jika ada masalah deployment, check:
1. Node.js version compatibility
2. Database connection
3. Environment variables
4. Port availability
5. File permissions

---

**Note**: Untuk production, sangat disarankan menggunakan PM2 atau process manager lainnya untuk auto-restart dan monitoring aplikasi.
