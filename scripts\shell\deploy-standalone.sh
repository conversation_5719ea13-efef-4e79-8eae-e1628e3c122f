#!/bin/bash

# PPID BPMP Deployment Script (Non-Docker)
echo "🚀 Starting PPID BPMP Deployment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm ci

echo "🗄️ Generating Prisma client..."
npx prisma generate

echo "🏗️ Building application..."
npm run build

echo "🔍 Verifying and copying required files..."
node verify-standalone.js

echo "📁 Preparing standalone build..."
if [ -d ".next/standalone" ]; then
    echo "✅ Standalone build created successfully"
    
    # Create logs directory
    mkdir -p logs
    
    echo "🎯 Deployment options:"
    echo "1. Run with Node.js directly: npm run deploy:vps"
    echo "2. Run with PM2 (recommended): npm run deploy:pm2"
    echo "3. Start manually: cd .next/standalone && node server.js"
    
    echo ""
    echo "🌐 Environment setup:"
    echo "- Make sure your .env file is configured"
    echo "- Database connection should be working"
    echo "- Port 3000 should be available (or set PORT env variable)"
    
    echo ""
    echo "✅ Build completed successfully!"
    echo "📍 Standalone files are in: .next/standalone/"
    
else
    echo "❌ Standalone build failed"
    exit 1
fi
