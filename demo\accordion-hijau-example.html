<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contoh Accordion Hijau - Compatible dengan CKEditor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 20px;
            background-color: #f9fafb;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #16a34a;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Accordion Styles */
        .accordion {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .accordion-header {
            background: linear-gradient(135deg, #16a34a, #15803d);
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            user-select: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: left;
            font-size: 16px;
            font-weight: 600;
        }
        
        .accordion-header:hover {
            background: linear-gradient(135deg, #15803d, #166534);
            transform: translateY(-1px);
        }
        
        .accordion-header.active {
            background: linear-gradient(135deg, #15803d, #166534);
        }
        
        .accordion-icon {
            transition: transform 0.3s ease;
            font-size: 18px;
        }
        
        .accordion-header.active .accordion-icon {
            transform: rotate(180deg);
        }
        
        .accordion-content {
            background: #f0fdf4;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            border-top: 1px solid #bbf7d0;
        }
        
        .accordion-content.active {
            max-height: 500px;
            padding: 20px;
        }
        
        .accordion-content p {
            margin: 0 0 15px 0;
            line-height: 1.6;
            color: #374151;
        }
        
        .accordion-content ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .accordion-content li {
            margin: 5px 0;
            color: #374151;
        }
        
        /* Variasi Hijau */
        .accordion-variant-1 .accordion-header {
            background: linear-gradient(135deg, #059669, #047857);
        }
        
        .accordion-variant-1 .accordion-header:hover,
        .accordion-variant-1 .accordion-header.active {
            background: linear-gradient(135deg, #047857, #065f46);
        }
        
        .accordion-variant-1 .accordion-content {
            background: #ecfdf5;
            border-top-color: #a7f3d0;
        }
        
        .accordion-variant-2 .accordion-header {
            background: linear-gradient(135deg, #22c55e, #16a34a);
        }
        
        .accordion-variant-2 .accordion-header:hover,
        .accordion-variant-2 .accordion-header.active {
            background: linear-gradient(135deg, #16a34a, #15803d);
        }
        
        .accordion-variant-2 .accordion-content {
            background: #f0f9ff;
            border-top-color: #bfdbfe;
        }
        
        /* CKEditor Compatible Classes */
        .ck-content .accordion {
            margin: 15px 0;
        }
        
        .ck-content .accordion-header {
            font-family: inherit;
        }
        
        .ck-content .accordion-content {
            font-family: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Contoh Accordion Hijau untuk CKEditor</h1>
        
        <h2>Accordion Standar (Hijau)</h2>
        <div class="accordion">
            <button class="accordion-header" onclick="toggleAccordion(this)">
                <span>📋 Informasi Umum PPID</span>
                <span class="accordion-icon">▼</span>
            </button>
            <div class="accordion-content">
                <p><strong>Pejabat Pengelola Informasi dan Dokumentasi (PPID)</strong> adalah pejabat yang bertanggung jawab di bidang penyimpanan, pendokumentasian, penyediaan, dan/atau pelayanan informasi di badan publik.</p>
                <ul>
                    <li>Menyediakan informasi publik yang akurat dan terpercaya</li>
                    <li>Melayani permohonan informasi dari masyarakat</li>
                    <li>Mengelola dokumentasi informasi publik</li>
                    <li>Memastikan transparansi dan akuntabilitas</li>
                </ul>
            </div>
        </div>
        
        <div class="accordion">
            <button class="accordion-header" onclick="toggleAccordion(this)">
                <span>📝 Cara Mengajukan Permohonan Informasi</span>
                <span class="accordion-icon">▼</span>
            </button>
            <div class="accordion-content">
                <p>Untuk mengajukan permohonan informasi, Anda dapat mengikuti langkah-langkah berikut:</p>
                <p><strong>Langkah 1:</strong> Siapkan dokumen identitas (KTP/SIM/Paspor)</p>
                <p><strong>Langkah 2:</strong> Isi formulir permohonan informasi secara lengkap</p>
                <p><strong>Langkah 3:</strong> Serahkan formulir melalui online atau datang langsung</p>
                <p><strong>Langkah 4:</strong> Tunggu proses verifikasi dan persetujuan</p>
                <p><strong>Langkah 5:</strong> Informasi akan diberikan sesuai jadwal yang ditentukan</p>
            </div>
        </div>
        
        <h2>Variasi Hijau Emerald</h2>
        <div class="accordion accordion-variant-1">
            <button class="accordion-header" onclick="toggleAccordion(this)">
                <span>🎯 Visi dan Misi BPMP Kaltim</span>
                <span class="accordion-icon">▼</span>
            </button>
            <div class="accordion-content">
                <p><strong>VISI:</strong></p>
                <p>Terwujudnya pendidikan berkualitas yang berkeadilan dan berkesetaraan di Provinsi Kalimantan Timur.</p>
                <p><strong>MISI:</strong></p>
                <ul>
                    <li>Meningkatkan kualitas layanan pendidikan</li>
                    <li>Memperkuat tata kelola pendidikan yang baik</li>
                    <li>Mengembangkan SDM pendidikan yang kompeten</li>
                    <li>Memperluas akses pendidikan yang berkualitas</li>
                </ul>
            </div>
        </div>
        
        <h2>Variasi Hijau Lime</h2>
        <div class="accordion accordion-variant-2">
            <button class="accordion-header" onclick="toggleAccordion(this)">
                <span>📊 Statistik dan Data</span>
                <span class="accordion-icon">▼</span>
            </button>
            <div class="accordion-content">
                <p>Berikut adalah data statistik terkini:</p>
                <p><strong>Jumlah Permohonan Informasi:</strong> 156 permohonan (2024)</p>
                <p><strong>Tingkat Kepuasan Layanan:</strong> 94.5%</p>
                <p><strong>Waktu Rata-rata Pelayanan:</strong> 3-5 hari kerja</p>
                <p><strong>Jenis Informasi Terpopuler:</strong></p>
                <ul>
                    <li>Data Sekolah dan Pendidikan</li>
                    <li>Regulasi dan Kebijakan</li>
                    <li>Profil Institusi</li>
                    <li>Dokumen Perencanaan</li>
                </ul>
            </div>
        </div>
        
        <hr style="margin: 30px 0; border: 1px solid #e5e7eb;">
        
        <h2>🔗 HTML Code untuk CKEditor</h2>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 14px; overflow-x: auto;">
<pre>&lt;div class="accordion"&gt;
    &lt;button class="accordion-header" onclick="toggleAccordion(this)" style="background: linear-gradient(135deg, #16a34a, #15803d); color: white; padding: 15px 20px; cursor: pointer; display: flex; justify-content: space-between; align-items: center; border: none; width: 100%; text-align: left; font-size: 16px; font-weight: 600; border-radius: 8px 8px 0 0;"&gt;
        &lt;span&gt;📋 Judul Accordion Anda&lt;/span&gt;
        &lt;span class="accordion-icon"&gt;▼&lt;/span&gt;
    &lt;/button&gt;
    &lt;div class="accordion-content" style="background: #f0fdf4; max-height: 0; overflow: hidden; border: 1px solid #d1d5db; border-top: 1px solid #bbf7d0; border-radius: 0 0 8px 8px;"&gt;
        &lt;div style="padding: 20px;"&gt;
            &lt;p&gt;Konten accordion Anda di sini...&lt;/p&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</pre>
        </div>
    </div>

    <script>
        function toggleAccordion(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.accordion-icon');
            
            // Close all other accordions
            document.querySelectorAll('.accordion-header').forEach(h => {
                if (h !== header) {
                    h.classList.remove('active');
                    h.nextElementSibling.classList.remove('active');
                    h.querySelector('.accordion-icon').style.transform = 'rotate(0deg)';
                }
            });
            
            // Toggle current accordion
            header.classList.toggle('active');
            content.classList.toggle('active');
            
            if (content.classList.contains('active')) {
                content.style.maxHeight = content.scrollHeight + 'px';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.maxHeight = '0';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        
        // Auto-open first accordion for demo
        window.addEventListener('load', function() {
            const firstAccordion = document.querySelector('.accordion-header');
            if (firstAccordion) {
                setTimeout(() => toggleAccordion(firstAccordion), 500);
            }
        });
    </script>
</body>
</html>
