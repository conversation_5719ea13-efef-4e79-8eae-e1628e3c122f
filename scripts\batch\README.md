# 📁 Batch Scripts Collection

This folder contains all batch (.bat) scripts used for testing, deployment, and maintenance of the PPID application.

## 📋 Script Categories

### 🚀 **Deployment Scripts**
- `deploy-standalone.bat` - Standalone deployment script

### 🔧 **Fix & Maintenance Scripts**
- `fix-nextauth-clean.bat` - Clean NextAuth configuration
- `fix-nextauth-error.bat` - Fix NextAuth errors

### 🧪 **Testing Scripts**

#### Accordion Feature Tests:
- `test-accordion-feature.bat` - General accordion functionality test
- `test-accordion-preservation.bat` - Test accordion preservation during tab switching
- `test-accordion-protection.bat` - Test accordion protection mechanisms
- `verify-accordion-workflow.bat` - Verify complete accordion workflow

#### Other Tests:
- `test-style-consistency.bat` - Test CSS style consistency
- `test-value-fix.bat` - Test value reference error fixes

## 🔧 **Usage Instructions**

### Running Scripts:
```cmd
cd d:\web2025\ppid\scripts\batch
script-name.bat
```

### From Root Directory:
```cmd
cd d:\web2025\ppid
scripts\batch\script-name.bat
```

## 📊 **Script Descriptions**

| Script | Purpose | Category |
|--------|---------|----------|
| `deploy-standalone.bat` | Deploy app without Docker | Deployment |
| `fix-nextauth-clean.bat` | Clean NextAuth config | Maintenance |
| `fix-nextauth-error.bat` | Fix NextAuth issues | Maintenance |
| `test-accordion-feature.bat` | Test accordion functionality | Testing |
| `test-accordion-preservation.bat` | Test accordion tab switching | Testing |
| `test-accordion-protection.bat` | Test accordion protection | Testing |
| `test-style-consistency.bat` | Test CSS consistency | Testing |
| `test-value-fix.bat` | Test value reference fixes | Testing |
| `verify-accordion-workflow.bat` | Verify accordion workflow | Testing |

## 🎯 **Development Workflow**

### 1. **Feature Testing:**
```cmd
test-accordion-feature.bat
verify-accordion-workflow.bat
```

### 2. **Protection Testing:**
```cmd
test-accordion-protection.bat
test-accordion-preservation.bat
```

### 3. **Deployment:**
```cmd
deploy-standalone.bat
```

### 4. **Maintenance:**
```cmd
fix-nextauth-clean.bat
test-style-consistency.bat
```

## 📁 **File Organization**

All batch scripts are now centralized in this folder for:
- ✅ **Better organization**
- ✅ **Easy maintenance** 
- ✅ **Clear categorization**
- ✅ **Consistent location**

---

*Scripts organized: August 8, 2025*  
*Total scripts: 9 files*
