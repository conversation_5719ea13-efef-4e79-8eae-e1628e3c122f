"use client";
import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import listPlugin from '@fullcalendar/list';
import idLocale from '@fullcalendar/core/locales/id';
import interactionPlugin from '@fullcalendar/interaction';
import Tooltip from 'tippy.js';
import 'tippy.js/dist/tippy.css';

const Kalender = () => {
  const [events, setEvents] = useState([]);
  const [viewMode, setViewMode] = useState('dayGridMonth');
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);

  useEffect(() => {
    // Fetch events from API
    fetch('/api/events')
      .then(response => response.json())
      .then(data => {
        // Format events for FullCalendar
        const formattedEvents = data.map(event => {
          // Parse extendedProps jika dalam format string
          let extendedProps = {};
          if (event.extendedProps) {
            try {
              extendedProps = typeof event.extendedProps === 'string' 
                ? JSON.parse(event.extendedProps) 
                : event.extendedProps;
            } catch (e) {
              console.error('Error parsing extendedProps:', e);
            }
          }
          
          return {
            id: event.id,
            title: event.title,
            start: new Date(event.start),
            end: event.end ? new Date(event.end) : null,
            allDay: event.allDay,
            backgroundColor: event.backgroundColor || getRandomColor(),
            borderColor: event.borderColor || event.backgroundColor || getRandomColor(),
            textColor: event.textColor || '#ffffff',
            description: event.description || '',
            extendedProps: {
              location: extendedProps.location || '',
              organizer: extendedProps.organizer || '',
              isPublic: extendedProps.isPublic !== undefined ? extendedProps.isPublic : true,
              ...extendedProps
            }
          };
        });
        setEvents(formattedEvents);
      })
      .catch(error => console.error('Error loading events:', error));
  }, []);

  const getRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      if (window.innerWidth < 768 && viewMode === 'dayGridMonth') {
        setViewMode('listWeek');
      } else if (window.innerWidth >= 768 && viewMode === 'listWeek') {
        setViewMode('dayGridMonth');
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Set initial view
    
    return () => window.removeEventListener('resize', handleResize);
  }, [viewMode]);

  return (
    <div className="w-full max-w-5xl p-4 mx-auto">
      <h2 className="mb-4 text-2xl font-bold text-center">
        Agenda Kegiatan BPMP Provinsi Kalimantan Timur Tahun {new Date().getFullYear()}
      </h2>
      <FullCalendar
        plugins={[dayGridPlugin, listPlugin, interactionPlugin]}
        initialView={windowWidth < 768 ? 'listWeek' : 'dayGridMonth'}
        events={events}
        height="auto"
        locale={idLocale}
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,listWeek'
        }}
        buttonText={{
          today: 'Hari Ini',
          month: 'Bulan',
          week: 'Minggu',
          list: 'Daftar'
        }}
        dayMaxEvents={true}
        eventClick={(info) => {
          setSelectedEvent(info.event);
          setShowEventModal(true);
        }}
        eventDisplay="block"
        displayEventTime={true}
        displayEventEnd={true}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          meridiem: false,
          hour12: false
        }}
        allDayText="Sepanjang hari"
        navLinks={true}
        firstDay={1}
        weekNumbers={false}
        weekText="Minggu"
        noEventsText="Tidak ada kegiatan"
        moreLinkText="lainnya"
        windowResize={function(arg) {
          if (arg.view.type === 'dayGridMonth' && window.innerWidth < 768) {
            arg.view.calendar.changeView('listWeek');
          } else if (arg.view.type === 'listWeek' && window.innerWidth >= 768) {
            arg.view.calendar.changeView('dayGridMonth');
          }
        }}
        eventDidMount={(info) => {
          // Format tanggal untuk tooltip
          const formatDate = (date) => {
            if (!date) return '';
            return new Date(date).toLocaleDateString('id-ID', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: 'numeric',
              minute: 'numeric'
            });
          };
          
          // Buat konten tooltip yang lebih informatif
          const event = info.event;
          const extendedProps = event.extendedProps || {};
          
          // Tampilkan tanggal selesai = end - 1 hari jika multi-day event (end exclusive)
          let displayEnd = event.end;
          if (event.end && event.start && event.end > event.start) {
            // Kurangi 1 hari (24 jam) dari end
            displayEnd = new Date(event.end.getTime() - 24 * 60 * 60 * 1000);
          }
          let tooltipContent = `
            <div class="p-2">
              <div class="font-bold">${event.title}</div>
              <div>Mulai: ${formatDate(event.start)}</div>
              ${event.end ? `<div>Selesai: ${formatDate(displayEnd)}</div>` : ''}
              ${extendedProps.location ? `<div>Lokasi: ${extendedProps.location}</div>` : ''}
              ${extendedProps.organizer ? `<div>Penyelenggara: ${extendedProps.organizer}</div>` : ''}
              ${event.extendedProps.description ? `<div>Deskripsi: ${event.extendedProps.description}</div>` : ''}
            </div>
          `;
          
          Tooltip(info.el, {
            content: tooltipContent,
            placement: 'top',
            trigger: 'mouseenter',
            hideOnClick: false,
            allowHTML: true,
            interactive: true,
            theme: 'light-border',
            maxWidth: 300
          });
        }}
      />
      
      {/* Modal Detail Event */}
      {showEventModal && selectedEvent && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md p-6 mx-4 bg-white rounded-lg shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold">{selectedEvent.title}</h3>
              <button 
                onClick={() => setShowEventModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            
            <div className="space-y-3">
              <p><strong>Mulai:</strong> {new Date(selectedEvent.start).toLocaleString('id-ID')}</p>
              {selectedEvent.end && (
                <p><strong>Selesai:</strong> {new Date(selectedEvent.end).toLocaleString('id-ID')}</p>
              )}
              {selectedEvent.extendedProps.location && (
                <p><strong>Lokasi:</strong> {selectedEvent.extendedProps.location}</p>
              )}
              {selectedEvent.extendedProps.organizer && (
                <p><strong>Penyelenggara:</strong> {selectedEvent.extendedProps.organizer}</p>
              )}
              {selectedEvent.extendedProps.description && (
                <p><strong>Deskripsi:</strong> {selectedEvent.extendedProps.description}</p>
              )}
            </div>
            
            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowEventModal(false)}
                className="px-4 py-2 text-white rounded bg-primary-600 hover:bg-primary-700"
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Kalender;
