// Script untuk memverifikasi dan memastikan kelengkapan standalone build
const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Standalone Build Completeness...\n');

const projectRoot = process.cwd();
const standaloneDir = path.join(projectRoot, '.next', 'standalone');
const staticDir = path.join(projectRoot, '.next', 'static');
const publicDir = path.join(projectRoot, 'public');

// Check if standalone build exists
if (!fs.existsSync(standaloneDir)) {
  console.error('❌ Standalone build not found. Run "npm run build" first.');
  process.exit(1);
}

console.log('✅ Standalone build directory exists');

// Function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    console.log(`⚠️  Source directory not found: ${src}`);
    return false;
  }
  
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  let filesCopied = 0;
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      if (!fs.existsSync(destPath) || fs.statSync(srcPath).mtime > fs.statSync(destPath).mtime) {
        fs.copyFileSync(srcPath, destPath);
        filesCopied++;
      }
    }
  }
  
  return filesCopied;
}

// Check and copy static files
console.log('\n📁 Checking static files...');
const standaloneStaticDir = path.join(standaloneDir, '.next', 'static');
if (fs.existsSync(staticDir)) {
  const copied = copyDir(staticDir, standaloneStaticDir);
  console.log(`✅ Static files: ${copied > 0 ? `${copied} files copied` : 'already up to date'}`);
} else {
  console.log('⚠️  No static files found');
}

// Check and copy public files
console.log('\n📂 Checking public files...');
const standalonePublicDir = path.join(standaloneDir, 'public');
if (fs.existsSync(publicDir)) {
  const copied = copyDir(publicDir, standalonePublicDir);
  console.log(`✅ Public files: ${copied > 0 ? `${copied} files copied` : 'already up to date'}`);
} else {
  console.log('⚠️  No public files found');
}

// Verify essential files
console.log('\n🔍 Verifying essential files...');

const essentialFiles = [
  { path: path.join(standaloneDir, 'server.js'), name: 'Server entry point' },
  { path: path.join(standaloneDir, 'package.json'), name: 'Package configuration' },
  { path: path.join(standaloneDir, '.next'), name: 'Next.js build directory' },
];

const cssFiles = [];
const jsFiles = [];

// Find CSS and JS files recursively
function findFiles(dir, extensions, fileList) {
  if (!fs.existsSync(dir)) return;
  
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findFiles(fullPath, extensions, fileList);
    } else {
      const ext = path.extname(file).toLowerCase();
      if (extensions.includes(ext)) {
        fileList.push(fullPath);
      }
    }
  });
}

// Find CSS files in standalone build
findFiles(standaloneStaticDir, ['.css'], cssFiles);
findFiles(standaloneDir, ['.js'], jsFiles);

essentialFiles.forEach(file => {
  if (fs.existsSync(file.path)) {
    console.log(`✅ ${file.name}: Found`);
  } else {
    console.log(`❌ ${file.name}: Missing`);
  }
});

console.log(`\n📊 File Statistics:`);
console.log(`   CSS files: ${cssFiles.length}`);
console.log(`   JS files: ${jsFiles.length}`);

if (cssFiles.length > 0) {
  console.log('\n🎨 CSS Files found:');
  cssFiles.slice(0, 5).forEach(file => {
    const relPath = path.relative(standaloneDir, file);
    const size = (fs.statSync(file).size / 1024).toFixed(2);
    console.log(`   - ${relPath} (${size} KB)`);
  });
  if (cssFiles.length > 5) {
    console.log(`   ... and ${cssFiles.length - 5} more files`);
  }
}

// Check for Tailwind CSS output
const tailwindIndicators = cssFiles.filter(file => {
  const content = fs.readFileSync(file, 'utf8');
  return content.includes('tailwind') || content.includes('tw-') || content.includes('@tailwind');
});

if (tailwindIndicators.length > 0) {
  console.log('✅ Tailwind CSS detected in build');
} else {
  console.log('⚠️  Tailwind CSS not clearly detected - check global.css');
}

// Check environment file
const envFiles = ['.env', '.env.local', '.env.production'];
let envFound = false;
envFiles.forEach(envFile => {
  if (fs.existsSync(path.join(projectRoot, envFile))) {
    console.log(`📋 Environment file: ${envFile} found`);
    envFound = true;
  }
});

if (!envFound) {
  console.log('⚠️  No environment files found - make sure to configure environment variables');
}

console.log('\n🎯 Deployment Checklist:');
console.log('   ✅ Standalone build created');
console.log('   ✅ Static files included');
console.log('   ✅ Public files included');
console.log('   ✅ CSS files generated');
console.log('   ✅ JavaScript bundles created');

console.log('\n📦 Standalone Build Ready!');
console.log(`📍 Location: ${standaloneDir}`);
console.log(`🚀 To start: cd .next/standalone && node server.js`);

// Create deployment verification file
const deployInfo = {
  buildTime: new Date().toISOString(),
  nodeVersion: process.version,
  cssFiles: cssFiles.length,
  jsFiles: jsFiles.length,
  standaloneReady: true
};

fs.writeFileSync(
  path.join(standaloneDir, 'deployment-info.json'), 
  JSON.stringify(deployInfo, null, 2)
);

console.log('📄 Deployment info saved to deployment-info.json');
