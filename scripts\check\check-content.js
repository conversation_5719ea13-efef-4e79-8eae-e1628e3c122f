const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkPost() {
  try {
    // Check all posts
    const posts = await prisma.post.findMany({
      select: { id: true, title: true, content: true, published: true }
    });
    
    console.log(`Found ${posts.length} posts total`);
    
    posts.forEach((post, index) => {
      console.log(`\n--- Post ${index + 1} ---`);
      console.log('ID:', post.id);
      console.log('Title:', post.title);
      console.log('Published:', post.published);
      console.log('Content length:', post.content ? post.content.length : 0);
      
      if (post.content) {
        console.log('Has HTML tags:', /<[^>]+>/.test(post.content));
        console.log('Has <details>:', post.content.includes('<details>'));
        console.log('Has encoded HTML (&lt;):', post.content.includes('&lt;'));
        console.log('First 200 chars:', post.content.substring(0, 200));
      }
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkPost();
