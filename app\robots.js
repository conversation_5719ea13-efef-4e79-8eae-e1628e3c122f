import { config, getProductionUrl } from '../lib/config';

export default function robots() {
  const baseUrl = getProductionUrl();

  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/dashboard/',
        '/admin/',
        '/api/',
        '/_next/',
        '/uploads/private/',
        '/login',
        '/unauthorized',
        '/404',
        '/500',
      ],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
