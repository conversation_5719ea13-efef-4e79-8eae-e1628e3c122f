'use client';

import { useEffect, useRef, useCallback } from 'react';
import { toast } from 'react-hot-toast';

/**
 * Hook untuk auto-save functionality
 * Menyimpan draft postingan secara otomatis setiap interval tertentu
 */
export function useAutoSave({
  formData,
  postId = null,
  interval = 30000, // 30 detik
  enabled = true,
  onSave = null
}) {
  const lastSavedData = useRef(null);
  const saveTimeoutRef = useRef(null);
  const isSavingRef = useRef(false);

  // Fungsi untuk save draft
  const saveDraft = useCallback(async (data) => {
    if (isSavingRef.current) return;
    
    try {
      isSavingRef.current = true;
      
      const endpoint = postId ? `/api/posts/${postId}/draft` : '/api/posts/draft';
      const method = postId ? 'PUT' : 'POST';
      
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          isDraft: true,
          autoSaved: true,
          autoSavedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save draft');
      }

      lastSavedData.current = JSON.stringify(data);
      
      // Show subtle notification
      toast.success('Draft tersimpan otomatis', {
        duration: 2000,
        position: 'bottom-right',
        style: {
          backgroundColor: '#f0f9ff',
          border: '1px solid #0284c7',
          color: '#0284c7',
          fontSize: '14px'
        }
      });

      if (onSave) {
        onSave(data);
      }

    } catch (error) {
      console.error('Auto-save error:', error);
      // Don't show error toast for auto-save failures to avoid annoying users
    } finally {
      isSavingRef.current = false;
    }
  }, [postId, onSave]);

  // Check if data has changed
  const hasDataChanged = useCallback((currentData) => {
    const currentDataString = JSON.stringify(currentData);
    return currentDataString !== lastSavedData.current;
  }, []);

  // Schedule auto-save
  const scheduleAutoSave = useCallback((data) => {
    if (!enabled || !hasDataChanged(data)) return;

    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Schedule new save
    saveTimeoutRef.current = setTimeout(() => {
      // Validate minimum content before saving
      if (data.title && data.title.length >= 3 && data.content && data.content.length >= 10) {
        saveDraft(data);
      }
    }, 3000); // Wait 3 seconds after last change before saving

  }, [enabled, hasDataChanged, saveDraft]);

  // Auto-save effect
  useEffect(() => {
    if (!enabled) return;

    scheduleAutoSave(formData);

    // Cleanup timeout on unmount
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [formData, scheduleAutoSave, enabled]);

  // Interval-based auto-save (fallback)
  useEffect(() => {
    if (!enabled) return;

    const intervalId = setInterval(() => {
      if (hasDataChanged(formData) && !isSavingRef.current) {
        // Only save if there's substantial content
        if (formData.title && formData.title.length >= 3 && 
            formData.content && formData.content.length >= 10) {
          saveDraft(formData);
        }
      }
    }, interval);

    return () => clearInterval(intervalId);
  }, [formData, interval, enabled, hasDataChanged, saveDraft]);

  // Manual save function
  const saveNow = useCallback(() => {
    if (!isSavingRef.current) {
      saveDraft(formData);
    }
  }, [formData, saveDraft]);

  // Initialize last saved data
  useEffect(() => {
    if (lastSavedData.current === null) {
      lastSavedData.current = JSON.stringify(formData);
    }
  }, [formData]);

  return {
    saveNow,
    isSaving: isSavingRef.current,
    hasUnsavedChanges: hasDataChanged(formData)
  };
}
