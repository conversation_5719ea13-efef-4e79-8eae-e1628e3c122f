@echo off
echo ===============================================
echo    Style Consistency Fix Test
echo ===============================================

echo.
echo [1/3] Testing CKEditor Styling...
echo Looking for line-height: 1.6 in SimpleHTMLEditor.jsx
findstr /n "line-height: 1.6" app\components\SimpleHTMLEditor.jsx
echo.

echo [2/3] Testing SafeContentRenderer Styling...  
echo Looking for unified-content class in SafeContentRenderer.jsx
findstr /n "unified-content" app\components\SafeContentRenderer.jsx
echo.

echo [3/3] Testing Global CSS...
echo Looking for unified-content rules in globals.css
findstr /n "unified-content" app\globals.css
echo.

echo ===============================================
echo ✅ Style Consistency Fix Applied!
echo ===============================================
echo.
echo Next Steps:
echo 1. Test create new content
echo 2. Edit existing content  
echo 3. Verify styles remain consistent
echo.
pause
