import Link from 'next/link';
import Image from 'next/image';
import { prisma } from '../../../../lib/prisma';
import Nav from '../../../components/Nav';
import { formatDate } from '../../../../lib/utils';
import { notFound } from 'next/navigation';

export async function generateMetadata(props) {
  const params = await props.params;
  const { slug } = params;
  const tag = await prisma.tag.findUnique({
    where: { slug },
  });

  if (!tag) {
    return {
      title: 'Tag tidak ditemukan',
    };
  }

  return {
    title: `Postingan dengan tag: ${tag.name}`,
    description: tag.description || `Daftar postingan dengan tag ${tag.name}`,
  };
}

async function getPostsByTag(slug) {
  const tag = await prisma.tag.findUnique({
    where: { slug },
    include: {
      tagsonposts: {
        include: {
          post: {
            include: {
              tagsonposts: {
                include: {
                  tag: true,
                },
              },
              user: {
                select: {
                  username: true,
                },
              },
            },
          },
        },
      },
    },
  });
  
  if (!tag) {
    return null;
  }
  
  // Transformasi data untuk mendapatkan daftar post
  const posts = tag.tagsonposts.map(({ post }) => post).filter(post => post.published);
  
  return { tag, posts };
}

export default async function TagPage(props) {
  const params = await props.params;
  const { slug } = params;
  const data = await getPostsByTag(slug);

  if (!data) {
    notFound();
  }

  const { tag, posts } = data;

  return (
    <>
      <Nav />
      <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
        <h1 className="mb-2 text-3xl font-bold text-center text-primary-800">
          {tag.name}
        </h1>
        {tag.description && (
          <p className="mb-8 text-center text-gray-600">{tag.description}</p>
        )}
        
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.length > 0 ? (
            posts.map((post) => (
              <div key={post.id} className="overflow-hidden transition-shadow bg-white rounded-lg shadow-md hover:shadow-lg">
                {post.featuredImage && (
                  <div className="relative w-full h-48">
                    <Image
                      src={post.featuredImage}
                      alt={post.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      quality={70}
                    />
                  </div>
                )}
                <div className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {post.tagsonposts.map(({ tag }) => (
                      <Link 
                        key={tag.id} 
                        href={`/posts/tag/${tag.slug}`}
                        prefetch={false}
                        className="px-2 py-1 text-xs font-medium text-primary-700 bg-primary-100 rounded-full hover:bg-primary-200"
                      >
                        {tag.name}
                      </Link>
                    ))}
                  </div>
                  <h2 className="mb-2 text-xl font-bold text-gray-900">
                    <Link href={`/posts/${post.slug}`} prefetch={false} className="hover:text-primary-600">
                      {post.title}
                    </Link>
                  </h2>
                  {post.excerpt && (
                    <p className="mb-4 text-gray-700">{post.excerpt}</p>
                  )}
                  <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                    <span>Oleh: {post.user.username}</span>
                    <span>{formatDate(post.publishedAt)}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-lg text-gray-600">Belum ada postingan dengan tag ini.</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}