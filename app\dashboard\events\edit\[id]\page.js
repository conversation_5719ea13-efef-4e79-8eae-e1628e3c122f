"use client";
import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
// Removed local Sidebar import to prevent duplicate sidebar
// import Sidebar from '../../../../components/Sidebar';
import { 
  DocumentTextIcon, 
  CalendarIcon, 
  ClockIcon, 
  MapPinIcon, 
  UserIcon 
} from '@heroicons/react/24/outline';

export default function EditEventPage(props) {
  const params = use(props.params);
  const router = useRouter();
  const { id } = params;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    location: '',
    organizer: '',
    description: '',
    isPublic: true,
    backgroundColor: '#3788d8',
    borderColor: '#3788d8',
    textColor: '#ffffff'
  });

  useEffect(() => {
    // Fetch event data
    const fetchEvent = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/events/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch event');
        }
        
        const event = await response.json();
        
        // Parse dates
        const startDate = new Date(event.start);
        const endDate = event.end ? new Date(event.end) : null;
        
        // Parse extendedProps
        let extendedProps = {};
        if (event.extendedProps) {
          try {
            extendedProps = typeof event.extendedProps === 'string' 
              ? JSON.parse(event.extendedProps) 
              : event.extendedProps;
          } catch (e) {
            console.error('Error parsing extendedProps:', e);
          }
        }
        
        // Format for form
        setFormData({
          title: event.title || '',
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate ? endDate.toISOString().split('T')[0] : '',
          startTime: '00:00',
          endTime: endDate ? endDate.toTimeString().slice(0, 5) : '',
          location: extendedProps.location || '',
          organizer: extendedProps.organizer || '',
          description: event.description || '',
          isPublic: extendedProps.isPublic !== undefined ? extendedProps.isPublic : true,
          backgroundColor: event.backgroundColor || '#3788d8',
          borderColor: event.borderColor || '#3788d8',
          textColor: event.textColor || '#ffffff'
        });
      } catch (error) {
        console.error('Error fetching event:', error);
        alert('Terjadi kesalahan saat mengambil data kegiatan');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEvent();
    }
  }, [id]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      const response = await fetch(`/api/events/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Gagal memperbarui kegiatan');
      }
      
      alert('Kegiatan berhasil diperbarui!');
      router.push('/dashboard/events');
    } catch (error) {
      console.error('Error updating event:', error);
      alert('Terjadi kesalahan saat memperbarui kegiatan: ' + error.message);
    } finally {
      setSaving(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('user');
    router.push('/login');
  };

  return (
    <>
      {/* Header */}
      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900">Edit Kegiatan</h1>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Kembali
          </button>
        </div>
      </header>

      <main className="py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="px-4 sm:px-0"
        >
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-16 h-16 border-4 border-t-4 border-gray-200 rounded-full border-t-primary-600 animate-spin"></div>
            </div>
          ) : (
            <div className="p-6 bg-white rounded-lg shadow">
              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 gap-6 mb-6 md:grid-cols-2">
                  {/* Judul Kegiatan */}
                  <div className="col-span-2">
                    <label htmlFor="title" className="block mb-2 text-sm font-medium text-gray-700">
                      Judul Kegiatan <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <DocumentTextIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        required
                        className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Masukkan judul kegiatan"
                      />
                    </div>
                  </div>

                  {/* Tanggal Mulai */}
                  <div>
                    <label htmlFor="startDate" className="block mb-2 text-sm font-medium text-gray-700">
                      Tanggal Mulai <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="date"
                        id="startDate"
                        name="startDate"
                        value={formData.startDate}
                        onChange={handleChange}
                        required
                        className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                  </div>

                  {/* Tanggal Selesai */}
                  <div>
                    <label htmlFor="endDate" className="block mb-2 text-sm font-medium text-gray-700">
                      Tanggal Selesai
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="date"
                        id="endDate"
                        name="endDate"
                        value={formData.endDate}
                        onChange={handleChange}
                        min={formData.startDate}
                        className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                  </div>

                  {/* Waktu */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="startTime" className="block mb-2 text-sm font-medium text-gray-700">
                        Waktu Mulai
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <ClockIcon className="w-5 h-5 text-gray-400" />
                        </div>
                        <input
                          type="time"
                          id="startTime"
                          name="startTime"
                          value={formData.startTime}
                          onChange={handleChange}
                          className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="endTime" className="block mb-2 text-sm font-medium text-gray-700">
                        Waktu Selesai
                      </label>
                      <input
                        type="time"
                        id="endTime"
                        name="endTime"
                        value={formData.endTime}
                        onChange={handleChange}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                  </div>

                  {/* Lokasi */}
                  <div>
                    <label htmlFor="location" className="block mb-2 text-sm font-medium text-gray-700">
                      Lokasi
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <MapPinIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleChange}
                        className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Masukkan lokasi kegiatan"
                      />
                    </div>
                  </div>

                  {/* Penyelenggara */}
                  <div>
                    <label htmlFor="organizer" className="block mb-2 text-sm font-medium text-gray-700">
                      Penyelenggara
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <UserIcon className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="organizer"
                        name="organizer"
                        value={formData.organizer}
                        onChange={handleChange}
                        className="block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Masukkan penyelenggara kegiatan"
                      />
                    </div>
                  </div>

                  {/* Status Kegiatan */}
                  <div className="col-span-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        name="isPublic"
                        checked={formData.isPublic}
                        onChange={handleChange}
                        className="w-4 h-4 border-gray-300 rounded text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Tampilkan di kalender publik</span>
                    </label>
                  </div>

                  {/* Deskripsi */}
                  <div className="col-span-2">
                    <label htmlFor="description" className="block mb-2 text-sm font-medium text-gray-700">
                      Deskripsi Kegiatan
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      rows="4"
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Masukkan deskripsi kegiatan"
                    ></textarea>
                  </div>

                  {/* Warna Event */}
                  <div className="col-span-2">
                    <label htmlFor="backgroundColor" className="block mb-2 text-sm font-medium text-gray-700">
                      Warna Latar Event
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        id="backgroundColor"
                        name="backgroundColor"
                        value={formData.backgroundColor}
                        onChange={handleChange}
                        className="w-10 h-10 p-1 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.backgroundColor}
                        onChange={(e) => setFormData({...formData, backgroundColor: e.target.value})}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="#3788d8"
                      />
                    </div>
                  </div>

                  {/* Warna Border */}
                  <div className="col-span-2">
                    <label htmlFor="borderColor" className="block mb-2 text-sm font-medium text-gray-700">
                      Warna Border Event
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        id="borderColor"
                        name="borderColor"
                        value={formData.borderColor}
                        onChange={handleChange}
                        className="w-10 h-10 p-1 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.borderColor}
                        onChange={(e) => setFormData({...formData, borderColor: e.target.value})}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="#3788d8"
                      />
                    </div>
                  </div>

                  {/* Warna Teks */}
                  <div className="col-span-2">
                    <label htmlFor="textColor" className="block mb-2 text-sm font-medium text-gray-700">
                      Warna Teks Event
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        id="textColor"
                        name="textColor"
                        value={formData.textColor}
                        onChange={handleChange}
                        className="w-10 h-10 p-1 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.textColor}
                        onChange={(e) => setFormData({...formData, textColor: e.target.value})}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-4 py-2 mr-4 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    disabled={saving}
                    className="px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md shadow-sm bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    {saving ? 'Menyimpan...' : 'Simpan Perubahan'}
                  </button>
                </div>
              </form>
            </div>
          )}
        </motion.div>
      </main>
    </>
  );
}
