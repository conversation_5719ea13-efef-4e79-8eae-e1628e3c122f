# Penghapusan Card "Laporan Layanan" dari <PERSON> Informasi

## Deskripsi
Menghapus card "Laporan Layanan" dari halaman informasi publik sesuai permintaan pengguna.

## Perubahan yang Dilakukan

### File: `app/components/cardData.jsx`

#### 1. Penghapusan Card Object
Menghapus object card dengan properties:
```jsx
{
    title: 'Laporan Layanan',
    description: 'Lihat laporan layanan informasi.',
    icon: <DocumentCheckIcon className="w-6 h-6" />,
    action: 'link',
    link: '/laporan-layanan.pdf',
    category: 'Dokumen'
}
```

#### 2. Cleanup Import
Menghapus `DocumentCheckIcon` dari import statement karena tidak lagi digunakan:
```jsx
// Sebelum
import { 
    DocumentTextIcon, 
    ClipboardDocumentListIcon, 
    DocumentDuplicateIcon, 
    DocumentCheckIcon,  // ← DIHAPUS
    QuestionMarkCircleIcon, 
    ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';

// Setelah
import { 
    DocumentTextIcon, 
    ClipboardDocumentListIcon, 
    DocumentDuplicateIcon, 
    QuestionMarkCircleIcon, 
    ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';
```

## Card yang Tersisa di Halaman Informasi

Setelah penghapusan, halaman informasi masih memiliki 5 card:

1. **Maklumat Pelayanan** (Dokumen)
   - Link ke `/maklumat.pdf`
   
2. **Formulir Permohonan** (Formulir)
   - Link ke `/permohonan`
   
3. **Formulir Keberatan** (Formulir)
   - Link ke `/keberatan`
   
4. **FAQ** (Informasi)
   - Modal dengan konten FAQ
   
5. **Kontak Kami** (Informasi)
   - Modal dengan informasi kontak

## Dampak Perubahan

### ✅ Positif
- Mengurangi clutter pada halaman informasi
- Layout tetap responsif dan seimbang
- Tidak ada breaking changes pada komponen lain
- Import yang tidak terpakai telah dibersihkan

### ℹ️ Neutral
- Jumlah card berkurang dari 6 menjadi 5
- Kategori "Dokumen" sekarang hanya memiliki 1 item (Maklumat Pelayanan)
- Filter kategori masih berfungsi normal

## Status
✅ **Selesai** - Card "Laporan Layanan" berhasil dihapus
✅ **Verified** - Tidak ada error dalam komponen
✅ **Clean** - Import yang tidak terpakai telah dibersihkan

## Catatan
- Jika file `/laporan-layanan.pdf` ada di folder `public/`, file tersebut masih dapat diakses langsung via URL
- Untuk sepenuhnya menghapus akses, file PDF tersebut juga perlu dihapus dari folder `public/`
