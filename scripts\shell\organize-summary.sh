#!/bin/bash

echo "==============================================="
echo "    SHELL SCRIPTS ORGANIZATION - COMPLETE"
echo "==============================================="
echo ""

# Check scripts location
echo "[1/3] Checking shell scripts location..."
SHELL_DIR=$(dirname "$0")
cd "$SHELL_DIR"

if [ -d "." ]; then
    SCRIPT_COUNT=$(find . -name "*.sh" -not -path "./organize-summary.sh" | wc -l)
    echo "✅ Found $SCRIPT_COUNT shell scripts:"
    
    find . -name "*.sh" -not -path "./organize-summary.sh" -exec basename {} \; | while read script; do
        if [ -f "$script" ]; then
            SIZE=$(stat -f%z "$script" 2>/dev/null || stat -c%s "$script" 2>/dev/null || echo "0")
            echo "   - $script (${SIZE} bytes)"
        fi
    done
else
    echo "❌ Scripts directory not found"
fi
echo ""

# Verify root directory cleanup
echo "[2/3] Verifying root directory cleanup..."
ROOT_DIR="$SHELL_DIR/../.."
cd "$ROOT_DIR"

if ls *.sh 1> /dev/null 2>&1; then
    echo "❌ Some .sh files still in root directory:"
    ls *.sh | while read file; do
        echo "   - $file"
    done
else
    echo "✅ Root directory clean - no .sh files found"
fi
echo ""

# Show organized structure
echo "[3/3] Showing shell scripts categorization..."
echo ""
echo "🚀 Deployment Scripts:"
echo "   - deploy-standalone.sh"
echo ""
echo "🔧 Maintenance Scripts:"
echo "   - fix-nextauth-error.sh"
echo ""

echo "==============================================="
echo "✅ SHELL SCRIPTS ORGANIZATION COMPLETE!"
echo "==============================================="
echo ""
echo "📁 All .sh files moved to: scripts/shell/"
echo "📋 Total scripts organized: 2 files"
echo "📖 Documentation: scripts/shell/README.md"
echo ""
echo "🎯 Categories:"
echo "   - Deployment: 1 script"
echo "   - Maintenance: 1 script"
echo ""
echo "🚀 Usage: chmod +x scripts/shell/script-name.sh && ./scripts/shell/script-name.sh"
echo ""
