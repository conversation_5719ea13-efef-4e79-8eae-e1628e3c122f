# 🎭 Demo & Testing Files Collection

This folder contains all demo, test, and example files used for development, testing, and demonstration of the PPID application features.

## 📋 File Categories

### 🎭 **Demo Files**
- `accordion-feature-demo.html` - Interactive demo of accordion functionality
- `ckeditor-tab-demo.html` - CKEditor tab switching demonstration

### 🧪 **Test Files**
- `test-accordion-rendering.html` - Test accordion rendering in different browsers
- `test-button-types.html` - Test various button styles and types
- `test-decode.js` - Test data decoding functionality
- `test-decode-simple.js` - Simple decoding test

### 📝 **Example Files**
- `accordion-hijau-example.html` - Example of green-themed accordion implementation

### 🔧 **Test Components**
- `test-accordion/` - Next.js test page for accordion functionality
  - `page.js` - React component for testing accordion features

## 🔧 **Usage Instructions**

### **HTML Demo Files:**
```bash
# Open in browser directly
start demo\file-name.html

# Or with live server for better development
cd demo
python -m http.server 3000
# Then open http://localhost:3000/file-name.html
```

### **JavaScript Test Files:**
```bash
cd demo
node test-file-name.js
```

### **React Test Components:**
```bash
# Access via Next.js app
http://localhost:3000/test-accordion
```

## 📊 **File Descriptions**

| File | Type | Purpose | Status |
|------|------|---------|--------|
| `accordion-feature-demo.html` | Demo | Interactive accordion showcase | Active |
| `accordion-hijau-example.html` | Example | Green theme accordion | Reference |
| `ckeditor-tab-demo.html` | Demo | CKEditor tab functionality | Ready |
| `test-accordion-rendering.html` | Test | Accordion cross-browser test | Active |
| `test-button-types.html` | Test | Button styling validation | Ready |
| `test-decode.js` | Test | Data decoding functionality | Ready |
| `test-decode-simple.js` | Test | Simple decode operations | Ready |
| `test-accordion/page.js` | Component | Next.js accordion test page | Active |

## 🎯 **Development Workflow**

### 1. **Feature Development:**
```bash
# Test accordion features
start accordion-feature-demo.html
http://localhost:3000/test-accordion
```

### 2. **UI/UX Testing:**
```bash
# Test visual components
start test-button-types.html
start accordion-hijau-example.html
```

### 3. **Functionality Testing:**
```bash
# Test core functions
node test-decode.js
node test-decode-simple.js
```

### 4. **Browser Compatibility:**
```bash
# Test across browsers
start test-accordion-rendering.html
```

## 🌐 **Browser Support Testing**

### **Accordion Features:**
- Chrome ✅
- Firefox ✅  
- Safari ✅
- Edge ✅

### **CKEditor Integration:**
- Visual Mode ✅
- Source Mode ✅
- Tab Switching ✅

## 🔍 **Demo File Features**

### **accordion-feature-demo.html:**
- ✅ 4 accordion types (Info, Warning, Success, FAQ)
- ✅ Interactive expand/collapse
- ✅ Tailwind CSS styling
- ✅ Responsive design

### **test-accordion-rendering.html:**
- ✅ Cross-browser compatibility test
- ✅ Different accordion configurations
- ✅ Performance benchmarks
- ✅ Mobile responsiveness

### **accordion-hijau-example.html:**
- ✅ Custom green theme
- ✅ Alternative styling approach
- ✅ Design inspiration reference

## 📱 **Mobile Testing**

All demo files are responsive and tested on:
- 📱 Mobile devices (320px+)
- 📟 Tablets (768px+)
- 💻 Desktops (1024px+)

## 🛠 **Development Tools**

### **For HTML Files:**
- Live Server extension (VS Code)
- Browser Developer Tools
- Responsive design testing

### **For JavaScript Files:**
- Node.js runtime
- Console debugging
- Performance profiling

## 📈 **Performance Metrics**

Demo files track:
- Load time optimization
- Interactive element responsiveness
- Memory usage efficiency
- Cross-browser consistency

## 🎨 **Design Showcase**

These demos showcase:
- ✅ **Accordion implementations** with different themes
- ✅ **Interactive behaviors** and animations
- ✅ **Responsive design** patterns
- ✅ **Accessibility features** and ARIA support

## 🔧 **Maintenance**

Regular updates include:
- Browser compatibility checks
- Performance optimizations
- New feature demonstrations
- Bug fix validations

---

*Demo files organized: August 8, 2025*  
*Total demo files: 8 files + 1 component folder*
