"use client";
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function NewUrlModalPage() {
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  async function submit(e) {
    e.preventDefault();
    setSaving(true);
    setError('');
    try {
      const res = await fetch('/api/urlmodals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title, content })
      });
      if (!res.ok) throw new Error('Gagal menyimpan');
      router.push('/dashboard/urlmodals');
    } catch (e) {
      setError(e.message || 'Gagal menyimpan');
    } finally {
      setSaving(false);
    }
  }

  return (
    <div className="max-w-3xl p-4 bg-white border rounded">
      <h1 className="mb-4 text-xl font-semibold">Buat Modal Baru</h1>
      {error && <p className="mb-3 text-red-600">{error}</p>}
      <form onSubmit={submit} className="space-y-4">
        <div>
          <label className="block mb-1 text-sm font-medium">Judul</label>
          <input className="w-full p-2 border rounded" value={title} onChange={(e) => setTitle(e.target.value)} required />
        </div>
        <div>
          <label className="block mb-1 text-sm font-medium">Konten (HTML diizinkan)</label>
          <textarea className="w-full p-2 font-mono border rounded min-h-64" value={content} onChange={(e) => setContent(e.target.value)} placeholder="<p>Konten modal</p>" required />
        </div>
        <div className="flex gap-2">
          <button disabled={saving} className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50">{saving ? 'Menyimpan...' : 'Simpan'}</button>
        </div>
      </form>
    </div>
  );
}
