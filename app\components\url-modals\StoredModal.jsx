"use client";
import { useEffect, useMemo, useState } from "react";
import SafeContentRenderer from "../../components/SafeContentRenderer";

export default function StoredModal({ id, onClose, size = 'lg' }) {
  const [data, setData] = useState({ title: "", content: "" });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    let alive = true;
    async function load() {
      try {
        const res = await fetch(`/api/urlmodals/${id}`);
        if (!res.ok) throw new Error(`Gagal memuat modal (${res.status})`);
        const json = await res.json();
        if (!alive) return;
        setData(json);
      } catch (e) {
        setError(e.message || "Gagal memuat modal");
      } finally {
        setLoading(false);
      }
    }
    if (id) load();
    return () => { alive = false; };
  }, [id]);

  const widthClass = useMemo(() => {
    switch (size) {
      case 'sm':
        return 'sm:max-w-sm md:max-w-md lg:max-w-lg';
      case 'md':
        return 'sm:max-w-md md:max-w-lg lg:max-w-xl';
      case 'lg':
        return 'sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl';
      case 'xl':
        return 'sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl';
      case '2xl':
        return 'sm:max-w-2xl md:max-w-3xl lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl';
      case 'full':
        return 'w-screen max-w-none h-screen';
      default:
        return 'sm:max-w-lg md:max-w-xl lg:max-w-2xl';
    }
  }, [size]);

  const isFull = size === 'full';

  return (
    <div role="dialog" aria-modal="true" aria-labelledby="modal-title" className="fixed inset-0 z-[1000] flex items-center justify-center">
      <div className="absolute inset-0 bg-black/40" onClick={onClose} />
      <div className={`relative z-10 w-full ${widthClass} bg-white shadow-xl ${isFull ? 'rounded-none' : 'rounded-lg'} ${isFull ? '' : 'p-6'}`}>
        {/* Accessible title only (hidden visually) */}
        <h2 id="modal-title" className="sr-only">
          {loading ? "Memuat" : (error ? "Error" : "Dialog")}
        </h2>
        {/* Close button in the corner */}
        <button
          onClick={onClose}
          aria-label="Close"
          className={`absolute ${isFull ? 'top-4 right-4' : 'top-2 right-2'} inline-flex h-8 w-8 items-center justify-center rounded hover:bg-gray-100`}
        >
          ×
        </button>
        {/* Content */}
        <div className={`${isFull ? 'h-full overflow-y-auto p-6' : 'mt-4 max-h-[80vh] overflow-y-auto'} text-sm text-gray-800`}>
          {loading && <p>Sedang memuat...</p>}
          {!loading && error && <p className="text-red-600">{error}</p>}
          {!loading && !error && (
            <SafeContentRenderer content={data.content || ""} />
          )}
        </div>
      </div>
    </div>
  );
}
