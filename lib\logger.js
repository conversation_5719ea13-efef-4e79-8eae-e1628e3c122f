import fs from 'fs';
import path from 'path';
import { format } from 'date-fns';

// Pastikan direktori logs ada
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Rotasi log sederhana: hapus file lebih tua dari N hari (default 14)
const RETENTION_DAYS = parseInt(process.env.LOG_RETENTION_DAYS || '14', 10);
function pruneOldLogs() {
  try {
    const files = fs.readdirSync(logsDir).filter((f) => f.endsWith('.log'));
    const now = Date.now();
    const maxAgeMs = RETENTION_DAYS * 24 * 60 * 60 * 1000;
    for (const file of files) {
      // Nama file: yyyy-MM-dd.log → parse tanggal dari nama file; fallback ke mtime
      const match = file.match(/^(\d{4}-\d{2}-\d{2})\.log$/);
      let fileTimeMs = 0;
      if (match) {
        const d = new Date(match[1]);
        fileTimeMs = d.getTime();
      }
      if (!fileTimeMs || Number.isNaN(fileTimeMs)) {
        const stats = fs.statSync(path.join(logsDir, file));
        fileTimeMs = stats.mtimeMs;
      }
      if (now - fileTimeMs > maxAgeMs) {
        try {
          fs.unlinkSync(path.join(logsDir, file));
        } catch {}
      }
    }
  } catch {}
}
// Jalankan pruning pada load modul
pruneOldLogs();

// Nama file log berdasarkan tanggal
const getLogFileName = () => {
  const today = new Date();
  return path.join(logsDir, `${format(today, 'yyyy-MM-dd')}.log`);
};

// Format pesan log
const formatLogMessage = (level, message, details = null) => {
  const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  let logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  if (details) {
    if (details instanceof Error) {
      logMessage += `\nStack: ${details.stack || 'No stack trace available'}`;
    } else if (typeof details === 'object') {
      try {
        logMessage += `\nDetails: ${JSON.stringify(details, null, 2)}`;
      } catch (e) {
        logMessage += `\nDetails: [Object cannot be stringified]`;
      }
    } else {
      logMessage += `\nDetails: ${details}`;
    }
  }
  
  return logMessage + '\n\n';
};

// Tulis ke file log
const writeToLog = (message) => {
  const logFile = getLogFileName();
  fs.appendFileSync(logFile, message);
};

// Logger functions
const logger = {
  info: (message, details = null) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`);
    }
    writeToLog(formatLogMessage('info', message, details));
  },
  
  warn: (message, details = null) => {
    if (process.env.NODE_ENV !== 'production') {
      console.warn(`[WARN] ${message}`);
    }
    writeToLog(formatLogMessage('warn', message, details));
  },
  
  error: (message, error = null) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`);
      if (error) console.error(error);
    }
    writeToLog(formatLogMessage('error', message, error));
  },
  
  debug: (message, details = null) => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[DEBUG] ${message}`);
    }
    if (process.env.LOG_LEVEL === 'debug') {
      writeToLog(formatLogMessage('debug', message, details));
    }
  }
};

export default logger;