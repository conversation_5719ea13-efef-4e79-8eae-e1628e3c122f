'use client';

import dynamic from 'next/dynamic';

// Dynamically import editor components with SSR disabled
const CustomCKEditor = dynamic(
  () => import('./CustomCKEditor'),
  { 
    ssr: false,
    loading: () => <EditorLoadingComponent text="Loading Advanced Editor..." />
  }
);

const SimpleHTMLEditor = dynamic(
  () => import('./SimpleHTMLEditor'),
  { 
    ssr: false,
    loading: () => <EditorLoadingComponent text="Loading HTML Editor..." />
  }
);

const StableCKEditor = dynamic(
  () => import('./StableCKEditor'),
  { 
    ssr: false,
    loading: () => <EditorLoadingComponent text="Loading Basic Editor..." />
  }
);

// Loading component
function EditorLoadingComponent({ text = "Loading Editor..." }) {
  return (
    <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50 min-h-[600px]">
      <div className="w-6 h-6 mr-2 border-2 border-t-2 border-gray-500 rounded-full border-t-blue-600 animate-spin"></div>
      <span className="text-sm text-gray-500">{text}</span>
    </div>
  );
}

/**
 * Enhanced Post Editor Component dengan multiple editor options
 * - CustomCKEditor: Advanced dengan HTML Embed & Source Editing (experimental)
 * - SimpleHTMLEditor: Custom HTML insertion dengan modal
 * - StableCKEditor: Basic stable editor
 */
export default function PostEditor({ 
  content = '', 
  onChange, 
  placeholder = "Mulai menulis konten postingan...",
  height = 700,
  hasError = false,
  errorMessage = '',
  disabled = false,
  className = '',
  editorType = 'simple' // 'advanced', 'simple', 'basic'
}) {
  
  const handleEditorChange = (data) => {
    if (onChange && !disabled) {
      onChange(data);
    }
  };

  const editorConfig = {
    height,
    placeholder,
  };

  const renderEditor = () => {
    switch (editorType) {
      case 'advanced':
        return (
          <CustomCKEditor 
            data={content} 
            onChange={handleEditorChange}
            config={editorConfig}
            height={height}
          />
        );
      
      case 'simple':
        return (
          <SimpleHTMLEditor 
            data={content} 
            onChange={handleEditorChange}
            config={editorConfig}
            height={height}
          />
        );
      
      case 'basic':
      default:
        return (
          <StableCKEditor 
            data={content} 
            onChange={handleEditorChange}
            config={editorConfig}
          />
        );
    }
  };

  return (
    <div className={`post-editor ${className}`}>
      {/* Editor Type Indicator */}
      <div className="mb-2 flex items-center justify-between text-xs text-gray-500">
        <span>
          Editor: <strong className="text-gray-700">
            {editorType === 'advanced' && '✨ Advanced (HTML Embed)'}
            {editorType === 'simple' && '🛠️ Simple HTML'}
            {editorType === 'basic' && '📝 Basic'}
          </strong>
        </span>
      </div>
      
      <div className={`editor-wrapper ${hasError ? 'border border-red-300 rounded-md' : ''}`}>
        {renderEditor()}
      </div>
      
      {hasError && errorMessage && (
        <p className="mt-1 text-sm text-red-600">{errorMessage}</p>
      )}
      
      {/* Character count info */}
      <div className="mt-2 text-xs text-gray-500 flex justify-between">
        <span>
          {content ? `${content.replace(/<[^>]*>/g, '').length} karakter` : '0 karakter'}
        </span>
        <span className="text-gray-400">
          {editorType === 'advanced' && '✨ Fitur HTML Embed & Source Editing aktif'}
          {editorType === 'simple' && '🛠️ Custom HTML insertion tersedia'}
          {editorType === 'basic' && '📝 Editor dasar dengan fitur standar'}
        </span>
      </div>
    </div>
  );
}
