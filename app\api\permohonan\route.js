import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../lib/auth';

export async function GET(request) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can view permohonan
    if (!userData.role || userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengakses data permohonan' },
        { status: 403 }
      );
    }

    // Get query parameters for pagination and filtering
    const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 10;
  const statusParam = searchParams.get('status'); // comma-separated allowed
  const search = searchParams.get('search');
  const dateFrom = searchParams.get('dateFrom'); // yyyy-mm-dd
  const dateTo = searchParams.get('dateTo');     // yyyy-mm-dd
  const sortBy = searchParams.get('sortBy') || 'tanggalPermohonan';
  const sortDir = (searchParams.get('sortDir') || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc';
    
    // Build where clause for filtering
    const where = {};
    
    if (statusParam && statusParam !== '') {
      const statuses = statusParam.split(',').map(s => s.trim()).filter(Boolean);
      if (statuses.length === 1) {
        where.status = statuses[0];
      } else if (statuses.length > 1) {
        where.status = { in: statuses };
      }
    }
    
    if (search && search.trim() !== '') {
      where.OR = [
        { namaSesuaiKtp: { contains: search, mode: 'insensitive' } },
        { nik: { contains: search } },
        { alamatEmail: { contains: search, mode: 'insensitive' } },
        { informasiYangDiminta: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (dateFrom || dateTo) {
      where.tanggalPermohonan = {};
      if (dateFrom) {
        const fromDate = new Date(dateFrom + 'T00:00:00');
        where.tanggalPermohonan.gte = fromDate;
      }
      if (dateTo) {
        const toDate = new Date(dateTo + 'T23:59:59');
        where.tanggalPermohonan.lte = toDate;
      }
    }

    // Get total count for pagination
    const total = await prisma.permohonanInformasi.count({ where });
    
    // Get permohonan data with pagination
    const permohonanList = await prisma.permohonanInformasi.findMany({
      where,
      orderBy: {
        // whitelist sortBy fields
        [(['tanggalPermohonan','status','namaSesuaiKtp','createdAt'].includes(sortBy) ? sortBy : 'tanggalPermohonan')]: sortDir
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        tanggalPermohonan: true,
        kategoriPemohon: true,
        nik: true,
        namaSesuaiKtp: true,
        alamatEmail: true,
        nomorKontak: true,
        informasiYangDiminta: true,
        tujuanPermohonanInformasi: true,
        bentukInformasi: true,
        caraMendapatkanInformasi: true,
        status: true,
        catatanAdmin: true,
        tanggapanAdmin: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    
    return NextResponse.json({
      success: true,
      data: permohonanList,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching permohonan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data permohonan' },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can update permohonan
    if (!userData.role || userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengupdate data permohonan' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, status, catatanAdmin, tanggapanAdmin } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID permohonan diperlukan' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'diproses', 'selesai', 'ditolak'];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Status tidak valid' },
        { status: 400 }
      );
    }

    // Fetch old values for audit
    const old = await prisma.permohonanInformasi.findUnique({ where: { id } });
    const updatedPermohonan = await prisma.permohonanInformasi.update({
      where: { id },
      data: {
        ...(status && { status }),
        ...(catatanAdmin !== undefined && { catatanAdmin }),
        ...(tanggapanAdmin !== undefined && { tanggapanAdmin }),
        adminId: userData.id,
        updatedAt: new Date()
      },
      select: {
        id: true,
        status: true,
        catatanAdmin: true,
        tanggapanAdmin: true,
        updatedAt: true
      }
    });
    // Audit log for changed fields
    try {
      const changes = [];
      if (status && old.status !== status) changes.push({ field: 'status', oldValue: old.status, newValue: status });
      if (catatanAdmin !== undefined && old.catatanAdmin !== catatanAdmin) changes.push({ field: 'catatanAdmin', oldValue: old.catatanAdmin, newValue: catatanAdmin });
      if (tanggapanAdmin !== undefined && old.tanggapanAdmin !== tanggapanAdmin) changes.push({ field: 'tanggapanAdmin', oldValue: old.tanggapanAdmin, newValue: tanggapanAdmin });
      for (const c of changes) {
        await prisma.auditLog.create({
          data: {
            entityType: 'permohonan',
            entityId: id,
            action: 'update',
            field: c.field,
            oldValue: c.oldValue,
            newValue: c.newValue,
            userId: userData.id,
          }
        });
      }
    } catch (e) { console.error('AuditLog error', e); }

    return NextResponse.json({
      success: true,
      message: 'Permohonan berhasil diupdate',
      data: updatedPermohonan
    });

  } catch (error) {
    console.error('Error updating permohonan:', error);
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Permohonan tidak ditemukan' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate permohonan' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Akses ditolak. Silakan login terlebih dahulu.' }, { status: 401 });
    }
    const userData = await verifyToken(token);
    if (!userData || !userData.id || (userData.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Anda tidak memiliki izin untuk mengupdate data permohonan' }, { status: 403 });
    }

    const body = await request.json();
    const { action, ids, status } = body;

    if (action !== 'bulkUpdateStatus') {
      return NextResponse.json({ error: 'Aksi tidak dikenali' }, { status: 400 });
    }
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ error: 'Daftar ID diperlukan' }, { status: 400 });
    }
    const validStatuses = ['pending', 'diproses', 'selesai', 'ditolak'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json({ error: 'Status tidak valid' }, { status: 400 });
    }

    // Audit log for each changed status
    const oldPermohonan = await prisma.permohonanInformasi.findMany({ where: { id: { in: ids } } });
    const result = await prisma.permohonanInformasi.updateMany({
      where: { id: { in: ids } },
      data: { status, updatedAt: new Date(), adminId: userData.id },
    });
    try {
      for (const p of oldPermohonan) {
        if (p.status !== status) {
          await prisma.auditLog.create({
            data: {
              entityType: 'permohonan',
              entityId: p.id,
              action: 'bulkUpdate',
              field: 'status',
              oldValue: p.status,
              newValue: status,
              userId: userData.id,
            }
          });
        }
      }
    } catch (e) { console.error('AuditLog error', e); }

    return NextResponse.json({ success: true, updatedCount: result.count });
  } catch (error) {
    console.error('Error in bulk update:', error);
    return NextResponse.json({ error: 'Terjadi kesalahan saat melakukan bulk update' }, { status: 500 });
  }
}
