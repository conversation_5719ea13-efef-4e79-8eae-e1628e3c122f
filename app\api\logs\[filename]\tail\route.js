import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import logger from '../../../../../lib/logger';

function readLastLines(filePath, maxLines) {
  const fd = fs.openSync(filePath, 'r');
  try {
    const stats = fs.fstatSync(fd);
    const bufferSize = Math.min(64 * 1024, stats.size || 0); // read chunks up to 64KB
    let position = stats.size;
    let lines = [];
    let leftover = '';

    while (position > 0 && lines.length <= maxLines) {
      const readSize = Math.min(bufferSize, position);
      position -= readSize;
      const buffer = Buffer.alloc(readSize);
      fs.readSync(fd, buffer, 0, readSize, position);
      const chunk = buffer.toString('utf8') + leftover;
      const parts = chunk.split(/\r?\n/);
      leftover = parts.shift() || '';
      // accumulate from end
      for (let i = parts.length - 1; i >= 0; i--) {
        if (parts[i] !== undefined) lines.push(parts[i]);
        if (lines.length >= maxLines) break;
      }
    }

    if (lines.length < maxLines && leftover) {
      lines.push(leftover);
    }

    return lines.slice(0, maxLines).reverse().join('\n');
  } finally {
    fs.closeSync(fd);
  }
}

export async function GET(request, props) {
  const params = await props.params;
  try {
    const { filename } = await params;
    const url = new URL(request.url);
    const maxLines = Math.max(1, Math.min(5000, parseInt(url.searchParams.get('lines') || '500', 10)));

    if (!filename.match(/^\d{4}-\d{2}-\d{2}\.log$/)) {
      return NextResponse.json(
        { error: 'Format nama file tidak valid' },
        { status: 400 }
      );
    }

    const filePath = path.join(process.cwd(), 'logs', filename);
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'File log tidak ditemukan' },
        { status: 404 }
      );
    }

    const content = readLastLines(filePath, maxLines);

    return NextResponse.json({ filename, lines: maxLines, content });
  } catch (error) {
    logger.error('Error tailing log file:', error);
    return NextResponse.json(
      { error: 'Gagal membaca bagian akhir file log' },
      { status: 500 }
    );
  }
}
