#!/bin/bash

echo "==============================================="
echo "    NextAuth Error Fix - Clean Restart"
echo "==============================================="

echo ""
echo "[1/4] Stopping PM2 process..."
pm2 stop ppid-bpmp

echo ""
echo "[2/4] Cleaning build cache..."
rm -rf .next
echo "✅ Cache cleared"

echo ""
echo "[3/4] Installing dependencies..."
npm install
echo "✅ Dependencies installed"

echo ""
echo "[4/4] Starting application..."
pm2 start ecosystem.config.json

echo ""
echo "==============================================="
echo "✅ NextAuth Error Fix Applied Successfully!"
echo "==============================================="
echo ""
echo "Check logs: pm2 logs ppid-bpmp"
