import { prisma } from '../../../lib/prisma';
import { NextResponse } from 'next/server';

function coerceId(id) {
  return /^\d+$/.test(id) ? Number(id) : id;
}

export async function GET(_req, { params }) {
  const { id } = params;
  const whereId = coerceId(id);
  try {
    const item = await prisma.urlmodal.findUnique({
      where: { id: whereId },
      select: { id: true, title: true, content: true, createdAt: true, updatedAt: true }
    });
    if (!item) return NextResponse.json({ error: 'Not found' }, { status: 404 });
    return NextResponse.json(item);
  } catch (e) {
    return NextResponse.json({ error: 'Gagal memuat data' }, { status: 500 });
  }
}

export async function PUT(req, { params }) {
  const { id } = params;
  const whereId = coerceId(id);
  try {
    const body = await req.json();
    const { title, content } = body || {};
    if (!title || !content) {
      return NextResponse.json({ error: 'Judul dan konten wajib diisi' }, { status: 400 });
    }
    const updated = await prisma.urlmodal.update({
      where: { id: whereId },
      data: { title, content },
      select: { id: true, title: true, content: true, createdAt: true, updatedAt: true }
    });
    return NextResponse.json(updated);
  } catch (e) {
    return NextResponse.json({ error: 'Gagal memperbarui' }, { status: 500 });
  }
}

export async function DELETE(_req, { params }) {
  const { id } = params;
  const whereId = coerceId(id);
  try {
    await prisma.urlmodal.delete({ where: { id: whereId } });
    return NextResponse.json({ ok: true });
  } catch (e) {
    return NextResponse.json({ error: 'Gagal menghapus' }, { status: 500 });
  }
}
