'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion';
import Nav from './../components/Nav'
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  AdjustmentsHorizontalIcon,
  HomeIcon
} from '@heroicons/react/24/outline'

const LinkPage = () => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  // Updated color scheme with blue tones to match profil page
  const links = [
    { 
      title: 'Portal Data', 
      icon: ChartBarIcon, 
      link: 'https://data.kemdikbud.go.id/', 
      color: "from-blue-50 to-blue-100",
      description: "Portal data terpusat Kementerian Pendidikan dan Kebudayaan untuk mengakses berbagai informasi statistik pendidikan."
    },
    { 
      title: 'Dapodik', 
      icon: UserGroupIcon, 
      link: 'https://dapo.kemdikbud.go.id/', 
      color: "from-sky-50 to-sky-100",
      description: "Data Pokok Pendidikan untuk mengakses informasi sekolah, siswa, guru, dan tenaga kependidikan."
    },
    { 
      title: '<PERSON><PERSON><PERSON> Kita', 
      icon: AdjustmentsHorizontalIcon, 
      link: 'https://sekolah.data.kemdikbud.go.id/', 
      color: "from-indigo-50 to-indigo-100",
      description: "Platform informasi sekolah yang menyajikan data profil sekolah di seluruh Indonesia."
    },
    { 
      title: 'Rumah Pendidikan', 
      icon: HomeIcon, 
      link: 'https://rumah.pendidikan.go.id/', 
      color: "from-cyan-50 to-cyan-100",
      description: "Platform digital yang menyediakan berbagai layanan dan informasi pendidikan terpadu."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-200">
      <div className="absolute inset-0 bg-grid-blue/5"></div>
      <Nav />
      <div className="relative flex flex-col items-center justify-center min-h-screen py-10">
        <div className="container px-4 mx-auto text-center">
          <motion.h6 
            className="mb-8 text-5xl font-bold text-blue-900"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Tautan Penting
          </motion.h6>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {links.map((link, index) => (
              <motion.div
                key={link.title}
                className="cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                onHoverStart={() => setHoveredIndex(index)}
                onHoverEnd={() => setHoveredIndex(null)}
                onClick={() => window.open(link.link, "_blank")}
              >
                <div 
                  className={`bg-gradient-to-r ${link.color} p-6 rounded-lg shadow-md transition-all duration-300 ease-in-out ${
                    hoveredIndex === index ? 'ring-2 ring-blue-400 shadow-lg shadow-blue-200/50' : ''
                  } ${hoveredIndex !== null && hoveredIndex !== index ? 'opacity-50 blur-sm' : ''}`}
                >
                  <div className="flex items-center justify-center mb-4">
                    <link.icon className={`w-8 h-8 ${
                      index % 4 === 0 ? "text-blue-600" :
                      index % 4 === 1 ? "text-sky-600" : 
                      index % 4 === 2 ? "text-indigo-600" :
                      "text-cyan-600"
                    }`} />
                  </div>
                  <h2 className={`mb-4 text-xl font-semibold ${
                    index % 4 === 0 ? "text-blue-800" :
                    index % 4 === 1 ? "text-sky-800" :
                    index % 4 === 2 ? "text-indigo-800" :
                    "text-cyan-800"
                  }`}>{link.title}</h2>
                  <p className={`${
                    index % 4 === 0 ? "text-blue-700" :
                    index % 4 === 1 ? "text-sky-700" :
                    index % 4 === 2 ? "text-indigo-700" :
                    "text-cyan-700"
                  }`}>
                    {link.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkPage;