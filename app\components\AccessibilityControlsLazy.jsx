'use client';

import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';

// Dynamically import the heavy AccessibilityControls on the client only
const AccessibilityControls = dynamic(() => import('./AccessibilityControls'), { ssr: false });

export default function AccessibilityControlsLazy() {
  const [ready, setReady] = useState(false);

  // Defer mounting until idle to keep LCP fast
  useEffect(() => {
    if ('requestIdleCallback' in window) {
      // @ts-ignore
      const id = window.requestIdleCallback(() => setReady(true), { timeout: 1200 });
      return () => {
        // @ts-ignore
        window.cancelIdleCallback && window.cancelIdleCallback(id);
      };
    } else {
      const t = setTimeout(() => setReady(true), 600);
      return () => clearTimeout(t);
    }
  }, []);

  if (!ready) return null;
  return <AccessibilityControls />;
}
