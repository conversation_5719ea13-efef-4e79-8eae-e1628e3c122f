# 🐚 Shell Scripts Collection

This folder contains all shell scripts (.sh) used for deployment, maintenance, and automation tasks in the PPID application.

## 📋 Script Categories

### 🚀 **Deployment Scripts**
- `deploy-standalone.sh` - Standalone deployment for Linux/Unix systems

### 🔧 **Maintenance Scripts**
- `fix-nextauth-error.sh` - Fix NextAuth configuration issues on Linux/Unix

## 🔧 **Usage Instructions**

### **Making Scripts Executable:**
```bash
chmod +x scripts/shell/script-name.sh
```

### **Running Scripts:**
```bash
# From root directory
./scripts/shell/script-name.sh

# From shell directory
cd scripts/shell
./script-name.sh
```

### **With Parameters:**
```bash
./scripts/shell/deploy-standalone.sh [environment]
./scripts/shell/fix-nextauth-error.sh [config-file]
```

## 📊 **Script Descriptions**

| Script | Purpose | Platform | Status |
|--------|---------|----------|--------|
| `deploy-standalone.sh` | Deploy app without Docker | Linux/Unix | Active |
| `fix-nextauth-error.sh` | Fix NextAuth issues | Linux/Unix | Active |

## 🎯 **Development Workflow**

### 1. **Linux/Unix Deployment:**
```bash
chmod +x scripts/shell/deploy-standalone.sh
./scripts/shell/deploy-standalone.sh production
```

### 2. **Fix NextAuth Issues:**
```bash
chmod +x scripts/shell/fix-nextauth-error.sh
./scripts/shell/fix-nextauth-error.sh
```

## 🌐 **Platform Support**

### **Compatible Systems:**
- ✅ Linux (Ubuntu, CentOS, Debian, etc.)
- ✅ macOS
- ✅ Unix-based systems
- ✅ WSL (Windows Subsystem for Linux)
- ✅ Git Bash (Windows)

### **Requirements:**
- Bash shell (version 4.0+)
- Node.js and npm
- Git
- Appropriate system permissions

## 🔒 **Security Considerations**

### **Before Running:**
1. **Review script contents** before execution
2. **Set proper permissions** (chmod +x)
3. **Run with appropriate user** (avoid root when possible)
4. **Backup important data** before deployment scripts

### **File Permissions:**
```bash
# Recommended permissions
chmod 755 script-name.sh  # Owner: read/write/execute, Others: read/execute
chmod 700 script-name.sh  # Owner only: read/write/execute (more secure)
```

## 📝 **Script Development Guidelines**

### **Best Practices:**
```bash
#!/bin/bash
set -e  # Exit on any error
set -u  # Exit on undefined variables

# Script header with description
# Author, date, and version info
# Usage instructions
```

### **Error Handling:**
```bash
# Check if command succeeded
if ! command -v node >/dev/null 2>&1; then
    echo "Error: Node.js is not installed"
    exit 1
fi
```

### **Logging:**
```bash
# Create log files
LOG_FILE="./logs/deployment-$(date +%Y%m%d-%H%M%S).log"
exec > >(tee -a "$LOG_FILE")
exec 2>&1
```

## 🛠 **Troubleshooting**

### **Common Issues:**

#### **Permission Denied:**
```bash
chmod +x script-name.sh
```

#### **Command Not Found:**
```bash
# Make sure script is executable and path is correct
ls -la script-name.sh
```

#### **Line Ending Issues (Windows):**
```bash
# Convert Windows line endings to Unix
dos2unix script-name.sh
```

## 🔧 **Cross-Platform Compatibility**

### **Windows Users:**
- Use **Git Bash** or **WSL** to run shell scripts
- Convert line endings with `dos2unix` if needed
- Ensure proper path separators

### **macOS Users:**
- Scripts should work out of the box
- May need to install additional tools via Homebrew

## 📈 **Monitoring & Logging**

### **Script Execution Logs:**
```bash
# Create logs directory if not exists
mkdir -p logs

# Log script execution
./script-name.sh 2>&1 | tee logs/execution-$(date +%Y%m%d).log
```

### **Performance Monitoring:**
```bash
# Time script execution
time ./script-name.sh

# Monitor resource usage
top -p $$ &
./script-name.sh
```

## 🔄 **Integration with CI/CD**

These scripts can be integrated into:
- GitHub Actions workflows
- GitLab CI/CD pipelines
- Jenkins build processes
- Docker containers

### **Example GitHub Action:**
```yaml
- name: Run deployment script
  run: |
    chmod +x scripts/shell/deploy-standalone.sh
    ./scripts/shell/deploy-standalone.sh
```

## 📁 **File Organization**

All shell scripts are centralized here for:
- ✅ **Better organization** and maintenance
- ✅ **Easy discovery** and execution
- ✅ **Consistent location** across environments
- ✅ **Version control** friendly structure

---

*Scripts organized: August 8, 2025*  
*Total shell scripts: 2 files*
