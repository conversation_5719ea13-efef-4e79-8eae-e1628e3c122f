'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../../../context/AuthContext';

export default function EditUserPage(props) {
  const router = useRouter();
  const params = use(props.params);
  const id = params.id;
  const { user: currentUser } = useAuth();
  
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
  username: '',
  email: '',
  role: '',
  newPassword: '',
  confirmPassword: '',
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Fetch user data
    const fetchUser = async () => {
      try {
        setLoading(true);
  const response = await fetch(`/api/users/${id}`, { credentials: 'include' });
        
        if (!response.ok) {
          if (response.status === 401) {
            router.push('/login');
            return;
          }
          if (response.status === 403) {
            // If user is not allowed, go back to users list with message
            setError('Anda tidak memiliki izin untuk mengedit pengguna ini');
            return;
          }
          if (response.status === 404) {
            setError('Pengguna tidak ditemukan');
            return;
          }
          throw new Error('Gagal mengambil data pengguna');
        }
        
        const data = await response.json();
        setUser(data.user);
        setFormData(prev => ({
          ...prev,
          username: data.user.username,
          email: data.user.email,
          role: data.user.role?.toLowerCase?.() || data.user.role || '',
        }));
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUser();
  }, [id, router]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      // Build payload according to role
      const isAdmin = currentUser?.role?.toLowerCase() === 'admin';
      const payload = {};
      if (isAdmin) {
        if (formData.email && formData.email !== user.email) {
          payload.email = formData.email;
        }
        if (formData.role && formData.role !== user.role?.toLowerCase()) {
          payload.role = formData.role;
        }
      }
      if (formData.newPassword) {
        if (formData.newPassword !== formData.confirmPassword) {
          throw new Error('Konfirmasi password tidak cocok');
        }
        payload.password = formData.newPassword;
      }
      if (Object.keys(payload).length === 0) {
        throw new Error('Tidak ada perubahan untuk disimpan');
      }
      const response = await fetch(`/api/users/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Gagal memperbarui pengguna');
      }
      
      setSuccess(true);
      setTimeout(() => {
        router.push('/dashboard/users');
      }, 2000);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !user) {
    return (
      <div className="container p-4 mx-auto">
        <h1 className="mb-4 text-2xl font-bold">Edit Pengguna</h1>
        <div className="animate-pulse">
          <div className="w-1/3 h-8 mb-4 bg-gray-200 rounded"></div>
          <div className="h-8 mb-4 bg-gray-200 rounded"></div>
          <div className="h-8 mb-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container p-4 mx-auto">
        <h1 className="mb-4 text-2xl font-bold">Edit Pengguna</h1>
        <div className="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
          {error}
        </div>
        <Link href="/dashboard/users" className="text-blue-500 hover:underline">
          Kembali ke daftar pengguna
        </Link>
      </div>
    );
  }

  return (
    <div className="container p-4 mx-auto">
      <h1 className="mb-4 text-2xl font-bold">Edit Pengguna</h1>
      
      {success && (
        <div className="px-4 py-3 mb-4 text-green-700 bg-green-100 border border-green-400 rounded">
          Pengguna berhasil diperbarui! Mengalihkan...
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="max-w-md space-y-4">
        <div>
          <label className="block mb-2 font-bold text-gray-700">Username</label>
          <input
            type="text"
            value={formData.username}
            readOnly
            className="w-full px-3 py-2 leading-tight text-gray-500 bg-gray-100 border rounded shadow appearance-none"
          />
        </div>

        {currentUser?.role?.toLowerCase() === 'admin' && (
          <>
            <div>
              <label htmlFor="email" className="block mb-2 font-bold text-gray-700">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-3 py-2 leading-tight text-gray-700 border rounded shadow appearance-none focus:outline-none focus:shadow-outline"
                required
              />
            </div>
            <div>
              <label htmlFor="role" className="block mb-2 font-bold text-gray-700">Role</label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="w-full px-3 py-2 leading-tight text-gray-700 border rounded shadow appearance-none focus:outline-none focus:shadow-outline"
              >
                <option value="user">User</option>
                <option value="admin">Admin</option>
              </select>
            </div>
          </>
        )}

        <div className="pt-2">
          <label htmlFor="newPassword" className="block mb-2 font-bold text-gray-700">Password Baru</label>
          <input
            type="password"
            id="newPassword"
            name="newPassword"
            value={formData.newPassword}
            onChange={handleChange}
            className="w-full px-3 py-2 leading-tight text-gray-700 border rounded shadow appearance-none focus:outline-none focus:shadow-outline"
            placeholder="Kosongkan jika tidak berubah"
            minLength={6}
          />
        </div>
        <div>
          <label htmlFor="confirmPassword" className="block mb-2 font-bold text-gray-700">Konfirmasi Password</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            className="w-full px-3 py-2 leading-tight text-gray-700 border rounded shadow appearance-none focus:outline-none focus:shadow-outline"
            minLength={6}
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            type="submit"
            className="px-4 py-2 font-bold text-white bg-blue-500 rounded hover:bg-blue-700 focus:outline-none focus:shadow-outline"
            disabled={loading}
          >
            {loading ? 'Menyimpan...' : 'Simpan'}
          </button>
          <Link href="/dashboard/users" className="text-blue-500 hover:underline">Batal</Link>
        </div>
      </form>
    </div>
  );
}

