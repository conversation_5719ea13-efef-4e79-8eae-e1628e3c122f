import { prisma } from '../lib/prisma';
import { config, getProductionUrl } from '../lib/config';

export default async function sitemap() {
  const baseUrl = getProductionUrl();

  // Static pages with improved SEO data
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/informasi`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/posts`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/permohonan`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/keberatan`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/regulasi`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/profil`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/tautan`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/status`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.4,
    },
    {
      url: `${baseUrl}/tracking`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.4,
    },
  ];

  try {
    // Dynamic pages - Published Posts
    const posts = await prisma.post.findMany({
      where: { published: true },
      select: {
        slug: true,
        updatedAt: true,
        createdAt: true,
      },
      orderBy: { updatedAt: 'desc' },
    });

    const postPages = posts.map((post) => ({
      url: `${baseUrl}/posts/${post.slug}`,
      lastModified: post.updatedAt,
      changeFrequency: 'weekly',
      priority: 0.7,
    }));

    // Dynamic pages - Tags (perbaikan relasi melalui tagsonposts)
    const tags = await prisma.tag.findMany({
      where: {
        tagsonposts: {
          some: {
            post: {
              published: true,
            },
          },
        },
      },
      select: {
        slug: true,
        updatedAt: true,
        _count: {
          select: {
            tagsonposts: {
              where: { post: { published: true } },
            },
          },
        },
      },
    });

    const tagPages = tags.map((tag) => ({
      url: `${baseUrl}/posts/tag/${tag.slug}`,
      lastModified: tag.updatedAt,
      changeFrequency: 'weekly',
      priority: tag._count.tagsonposts > 5 ? 0.6 : 0.5,
    }));

    // Tag pages - using existing tags functionality
    // Note: Category functionality removed as 'kategori' field doesn't exist in post model
    // Using tags instead for better categorization
    
    return [...staticPages, ...postPages, ...tagPages];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return static pages only if database query fails
    return staticPages;
  }
}
