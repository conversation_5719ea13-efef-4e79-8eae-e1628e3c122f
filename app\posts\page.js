import Link from 'next/link';
import Image from 'next/image';
import { prisma } from '../../lib/prisma';
import Nav from './../components/Nav';
import { formatDate } from '../../lib/utils';

export const metadata = {
  title: 'Informasi Publik',
  description: 'Daftar informasi publik dan berita terkini dari BPMP Provinsi Kalimantan Timur. Akses informasi berkala, serta merta, dan setiap saat.',
  keywords: 'informasi publik, berita, PPID, BPMP, Kalimantan Timur, informasi berkala, informasi serta merta',
  openGraph: {
    title: 'Informasi Publik | PPID BPMP Prov. Kaltim',
    description: 'Daftar informasi publik dan berita terkini dari BPMP Provinsi Kalimantan Timur.',
    type: 'website',
    locale: 'id_ID',
  },
  robots: {
    index: true,
    follow: true,
  },
};

async function getPosts() {
  const posts = await prisma.post.findMany({
    where: {
      published: true,
    },
    include: {
      tagsonposts: {
        include: {
          tag: true,
        },
      },
      user: {
        select: {
          username: true,
        },
      },
    },
    orderBy: {
      publishedAt: 'desc',
    },
  });
  
  return posts;
}

export default async function PostsPage() {
  const posts = await getPosts();
  
  return (
    <>
      <Nav />
      <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
        <h1 className="mb-8 text-3xl font-bold text-center text-primary-800">
          Informasi Publik
        </h1>
        
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.length > 0 ? (
            posts.map((post) => (
              <div key={post.id} className="overflow-hidden transition-shadow bg-white rounded-lg shadow-md hover:shadow-lg">
                {post.featuredImage && (
                  <div className="relative w-full h-48">
                    <Image
                      src={post.featuredImage}
                      alt={post.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      quality={70}
                    />
                  </div>
                )}
                <div className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {post.tagsonposts.map(({ tag }) => (
                      <Link
                        key={tag.id}
                        href={`/posts/tag/${tag.slug}`}
                        prefetch={false}
                        className="px-2 py-1 text-xs font-medium text-primary-700 bg-primary-100 rounded-full hover:bg-primary-200"
                      >
                        {tag.name}
                      </Link>
                    ))}
                  </div>
                  <h2 className="mb-2 text-xl font-bold text-gray-900">
                    <Link href={`/posts/${post.slug}`} prefetch={false} className="hover:text-primary-600">
                      {post.title}
                    </Link>
                  </h2>
                  {post.excerpt && (
                    <p className="mb-4 text-gray-700">{post.excerpt}</p>
                  )}
                  <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                    <span>Oleh: {post.user.username}</span>
                    <span>{formatDate(post.publishedAt)}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-lg text-gray-600">Belum ada postingan yang dipublikasikan.</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}