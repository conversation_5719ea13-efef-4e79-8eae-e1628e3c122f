"use client";
import { useEffect, useMemo, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function UrlModalsPage() {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const pathname = usePathname();

  async function load() {
    setLoading(true);
    try {
      const res = await fetch('/api/urlmodals');
      if (!res.ok) throw new Error('Gagal memuat data');
      const json = await res.json();
      setItems(json);
    } catch (e) {
      setError(e.message || 'Gagal memuat');
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => { load(); }, []);

  const base = useMemo(() => (typeof window !== 'undefined' ? `${window.location.origin}${pathname}` : pathname), [pathname]);

  async function handleDelete(id) {
    if (!confirm('Yakin ingin menghapus modal ini?')) return;
    await fetch(`/api/urlmodals/${id}`, { method: 'DELETE' });
    load();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-semibold">Daftar Modal via URL</h1>
        <Link href="/dashboard/urlmodals/new" className="px-3 py-2 text-white bg-blue-600 rounded hover:bg-blue-700">Buat Modal</Link>
      </div>

      {loading && <p>Memuat...</p>}
      {error && <p className="text-red-600">{error}</p>}

      {!loading && !error && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border rounded">
            <thead>
              <tr className="text-left border-b">
                <th className="p-3">Judul</th>
                <th className="p-3">Dibuat</th>
                <th className="p-3">URL untuk disisipkan</th>
                <th className="p-3">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {items.map((it) => {
                // Gunakan URL relatif agar tetap di halaman saat ini
                const relativeUrl = `?modal=stored&modalData=${encodeURIComponent(JSON.stringify({ id: it.id }))}`;
                const absoluteHere = typeof window !== 'undefined' ? `${window.location.origin}${window.location.pathname}${relativeUrl}` : `${base}${relativeUrl}`;
                return (
                  <tr key={it.id} className="border-b">
                    <td className="p-3">{it.title}</td>
                    <td className="p-3">{new Date(it.createdAt).toLocaleString()}</td>
                    <td className="p-3">
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <input className="w-full p-2 border rounded" readOnly value={relativeUrl} />
                          <button className="px-2 py-1 text-sm text-white bg-gray-700 rounded" onClick={async () => { await navigator.clipboard.writeText(relativeUrl); }}>Copy</button>
                        </div>
                        <div className="text-xs text-gray-500">
                          Gunakan link relatif di konten/post agar tetap di halaman saat ini. Jika perlu versi absolut untuk halaman ini:
                          <button className="ml-2 underline text-blue-600" onClick={async () => { await navigator.clipboard.writeText(absoluteHere); }}>Copy absolut (halaman ini)</button>
                        </div>
                      </div>
                    </td>
                    <td className="p-3 flex items-center gap-2">
                      <Link href={`/dashboard/urlmodals/${it.id}/edit`} className="px-2 py-1 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600">Edit</Link>
                      <button className="px-2 py-1 text-sm text-white bg-red-600 rounded hover:bg-red-700" onClick={() => handleDelete(it.id)}>Hapus</button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
