-- CreateTable
CREATE TABLE `keberatan_informasi` (
    `id` VARCHAR(6) NOT NULL,
    `tangg<PERSON><PERSON><PERSON><PERSON><PERSON>an` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on` VARCHAR(191) NOT NULL,
    `nik` VARCHAR(20) NOT NULL,
    `namaSesuaiKtp` VARCHAR(255) NOT NULL,
    `alamatLengkapSesuaiKtp` TEXT NOT NULL,
    `alamatTinggalSaatIni` TEXT NOT NULL,
    `nomorKontak` VARCHAR(20) NOT NULL,
    `alamatEmail` VARCHAR(255) NOT NULL,
    `peker<PERSON>an` VARCHAR(255) NOT NULL,
    `topikKeberatan` TEXT NOT NULL,
    `maksudKeberatan` TEXT NOT NULL,
    `alasanKeberatan` VARCHAR(191) NOT NULL,
    `salinanKtpPath` VARCHAR(500) NULL,
    `pernyataanKeberatan` BOOLEAN NOT NULL DEFAULT false,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `catatanAdmin` TEXT NULL,
    `tanggapanAdmin` TEXT NULL,
    `adminId` VARCHAR(36) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `keberatan_informasi_adminId_idx`(`adminId`),
    INDEX `keberatan_informasi_createdAt_idx`(`createdAt`),
    INDEX `keberatan_informasi_kategoriPemohon_idx`(`kategoriPemohon`),
    INDEX `keberatan_informasi_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `url_modal` (
    `id` VARCHAR(36) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `content` LONGTEXT NOT NULL,
    `authorId` VARCHAR(36) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `url_modal_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
