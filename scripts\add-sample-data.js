// Script untuk menambahkan data sample ke database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleData() {
  try {
    console.log('🌱 Adding sample data to database...\n');

    // Add sample page visits
    console.log('📊 Adding page visits...');
    const pageVisits = [];
    const urls = ['/', '/posts', '/informasi', '/permohonan', '/keberatan', '/regulasi'];
    
    for (let i = 0; i < 50; i++) {
      const randomUrl = urls[Math.floor(Math.random() * urls.length)];
      const randomDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      
      pageVisits.push({
        id: `visit_${i}_${Date.now()}`,
        visitorId: `visitor_${Math.floor(Math.random() * 20)}`,
        url: randomUrl,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        referrer: i % 5 === 0 ? 'https://google.com' : null,
        timestamp: randomDate
      });
    }

    for (const visit of pageVisits) {
      await prisma.pagevisit.create({
        data: visit
      });
    }
    console.log(`✅ Added ${pageVisits.length} page visits`);

    // Add sample files
    console.log('📄 Adding files...');
    const files = [
      {
        id: `file_${Date.now()}_1`,
        originalName: 'Laporan Keuangan 2024.pdf',
        description: 'Laporan keuangan tahunan 2024',
        category: 'laporan',
        isPublic: true,
        path: '/uploads/laporan-keuangan-2024.pdf',
        size: 2048576,
        type: 'application/pdf'
      },
      {
        id: `file_${Date.now()}_2`,
        originalName: 'Formulir Permohonan Informasi.pdf',
        description: 'Formulir untuk permohonan informasi publik',
        category: 'formulir',
        isPublic: true,
        path: '/uploads/formulir-permohonan.pdf',
        size: 1024000,
        type: 'application/pdf'
      },
      {
        id: `file_${Date.now()}_3`,
        originalName: 'Dokumen Internal.doc',
        description: 'Dokumen internal tidak untuk publik',
        category: 'internal',
        isPublic: false,
        path: '/uploads/internal.doc',
        size: 512000,
        type: 'application/msword'
      },
      {
        id: `file_${Date.now()}_4`,
        originalName: 'Panduan PPID.pdf',
        description: 'Panduan layanan PPID',
        category: 'panduan',
        isPublic: true,
        path: '/uploads/panduan-ppid.pdf',
        size: 3072000,
        type: 'application/pdf'
      }
    ];

    for (const file of files) {
      await prisma.file.create({
        data: file
      });
    }
    console.log(`✅ Added ${files.length} files`);

    // Add sample events
    console.log('📅 Adding events...');
    const events = [
      {
        id: `event_${Date.now()}_1`,
        title: 'Sosialisasi PPID',
        start: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours later
        allDay: false,
        backgroundColor: '#3B82F6',
        description: 'Sosialisasi layanan PPID kepada masyarakat'
      },
      {
        id: `event_${Date.now()}_2`,
        title: 'Rapat Koordinasi',
        start: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        end: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours later
        allDay: false,
        backgroundColor: '#10B981',
        description: 'Rapat koordinasi tim PPID'
      },
      {
        id: `event_${Date.now()}_3`,
        title: 'Pelatihan Sistem Informasi',
        start: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        end: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000), // 4 hours later
        allDay: false,
        backgroundColor: '#8B5CF6',
        description: 'Pelatihan penggunaan sistem informasi baru'
      },
      {
        id: `event_${Date.now()}_4`,
        title: 'Workshop Transparansi',
        start: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        end: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000), // 6 hours later
        allDay: false,
        backgroundColor: '#F59E0B',
        description: 'Workshop tentang transparansi informasi publik'
      }
    ];

    for (const event of events) {
      await prisma.event.create({
        data: event
      });
    }
    console.log(`✅ Added ${events.length} events`);

    console.log('\n🎉 Sample data added successfully!');
    console.log('\nStatistics Summary:');
    console.log('- Page visits: 50 records');
    console.log('- Files: 4 records (3 public, 1 private)');
    console.log('- Events: 4 records (2 upcoming, 2 past)');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addSampleData();
