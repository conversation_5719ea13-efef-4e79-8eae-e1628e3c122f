'use client';

import { AuthProvider } from './context/AuthContext';
import { Toaster } from 'react-hot-toast';
import UrlModalHost from './components/url-modals/UrlModalHost';
import { Suspense, useState } from 'react';
import { usePathname } from 'next/navigation';
import { AnimatePresence, motion, useReducedMotion as useFmReducedMotion } from 'framer-motion';
import { useAccessibility } from './context/AccessibilityContext';

export default function ClientWrapper({ children }) {
  const pathname = usePathname();
  const { settings } = useAccessibility();
  const prefersReduced = useFmReducedMotion();

  // Registry of URL-loadable modals (lazy-loaded)
  const registry = {
    simple: () => import('./components/url-modals/SimpleModal'),
    stored: () => import('./components/url-modals/StoredModal'),
  };

  const reduce = Boolean(settings?.reducedMotion || prefersReduced);
  const variants = reduce
    ? { initial: {}, animate: {}, exit: {} }
    : {
        initial: { opacity: 0, y: 8 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -8 }
      };

  const [isExiting, setIsExiting] = useState(false);

  return (
    <AuthProvider>
      <AnimatePresence mode="wait" initial={false}>
        <motion.div
          key={pathname}
          initial={variants.initial}
          animate={variants.animate}
          exit={variants.exit}
          transition={reduce ? { duration: 0 } : { type: 'spring', stiffness: 220, damping: 26, mass: 0.9 }}
          onAnimationStart={(def) => def === 'exit' && setIsExiting(true)}
          onAnimationComplete={(def) => def === 'exit' && setIsExiting(false)}
          style={{ willChange: 'opacity, transform', pointerEvents: isExiting ? 'none' : 'auto' }}
        >
          {children}
        </motion.div>
      </AnimatePresence>

      {/* Global URL-driven modal host */}
      <Suspense fallback={null}>
        <UrlModalHost registry={registry} />
      </Suspense>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#fff',
            color: '#363636',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
            style: {
              borderLeft: '4px solid #10b981',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
            style: {
              borderLeft: '4px solid #ef4444',
            },
          },
        }}
      />
    </AuthProvider>
  );
}
