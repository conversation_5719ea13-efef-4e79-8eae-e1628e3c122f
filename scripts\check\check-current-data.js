// Script untuk memeriksa data yang ada di database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentData() {
  try {
    console.log('🔍 Checking current database data...\n');

    // Check page visits
    const pageVisitCount = await prisma.pagevisit.count();
    console.log(`📊 Page visits: ${pageVisitCount} records`);
    
    if (pageVisitCount > 0) {
      const sampleVisits = await prisma.pagevisit.findMany({
        take: 3,
        orderBy: { timestamp: 'desc' }
      });
      console.log('  Recent visits:');
      sampleVisits.forEach(visit => {
        console.log(`    - ${visit.url} (${visit.timestamp.toLocaleDateString()})`);
      });
    }

    // Check files
    const fileCount = await prisma.file.count();
    const publicFileCount = await prisma.file.count({ where: { isPublic: true } });
    console.log(`📄 Files: ${fileCount} total (${publicFileCount} public)`);
    
    if (fileCount > 0) {
      const sampleFiles = await prisma.file.findMany({
        take: 3,
        select: { originalName: true, isPublic: true, category: true }
      });
      console.log('  Sample files:');
      sampleFiles.forEach(file => {
        console.log(`    - ${file.originalName} (${file.category}, public: ${file.isPublic})`);
      });
    }

    // Check events
    const eventCount = await prisma.event.count();
    const upcomingEvents = await prisma.event.count({
      where: { start: { gte: new Date() } }
    });
    const pastEvents = await prisma.event.count({
      where: { start: { lt: new Date() } }
    });
    console.log(`📅 Events: ${eventCount} total (${upcomingEvents} upcoming, ${pastEvents} past)`);
    
    if (eventCount > 0) {
      const sampleEvents = await prisma.event.findMany({
        take: 3,
        select: { title: true, start: true },
        orderBy: { start: 'desc' }
      });
      console.log('  Sample events:');
      sampleEvents.forEach(event => {
        console.log(`    - ${event.title} (${event.start.toLocaleDateString()})`);
      });
    }

    console.log('\n✅ Database check completed!');

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentData();
