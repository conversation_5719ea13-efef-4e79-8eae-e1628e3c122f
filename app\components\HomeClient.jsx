'use client';
import { useEffect, useState, Suspense } from 'react';
import { motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  DocumentDuplicateIcon,
  QuestionMarkCircleIcon,
  BookOpenIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';
import Nav from './../components/Nav';
import Hero from './../components/Hero';

// Dynamic imports
const Footer = dynamic(() => import('./../components/Footer'), {
  loading: () => <div className="py-6 bg-primary-700"></div>,
  ssr: true
});

const ClientKalender = dynamic(() => import('./../components/ClientKalender'), {
  loading: () => <div className="flex items-center justify-center h-96">Memuat kalender...</div>,
  ssr: false
});

// Loading skeleton untuk card
const CardSkeleton = () => (
  <div className="w-full p-4 bg-white rounded-lg shadow-md animate-pulse sm:w-[180px]">
    <div className="w-12 h-12 mx-auto mb-4 bg-gray-200 rounded-full"></div>
    <div className="w-3/4 h-4 mx-auto mb-2 bg-gray-200 rounded"></div>
    <div className="w-full h-3 mx-auto mb-1 bg-gray-200 rounded"></div>
    <div className="w-2/3 h-3 mx-auto bg-gray-200 rounded"></div>
  </div>
);

export default function HomeClient() {
  // Cek preferensi reduced motion
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handleChange = () => setPrefersReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);
    
    // Tandai komponen sebagai sudah dimuat
    setIsLoaded(true);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  // Konfigurasi animasi
  const staggerContainer = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const slideInUp = {
    initial: { y: 50, opacity: 0 },
    animate: { y: 0, opacity: 1 }
  };
  
  const infoCards = [
    {
      title: "Informasi Berkala",
      description: "Informasi yang wajib disediakan dan diumumkan secara berkala",
      icon: <DocumentTextIcon className="w-6 h-6" />,
      link: "/regulasi?tab=informasi-berkala"
    },
    {
      title: "Informasi Setiap Saat",
      description: "Informasi yang wajib tersedia setiap saat",
      icon: <BookOpenIcon className="w-6 h-6" />,
      link: "/regulasi?tab=informasi-setiap-saat"
    },
    {
      title: "Informasi Serta Merta",
      description: "Informasi yang dapat mengancam hajat hidup orang banyak",
      icon: <DocumentDuplicateIcon className="w-6 h-6" />,
      link: "/regulasi?tab=informasi-serta-merta"
    },
    {
      title: "Informasi Publik",
      description: "Daftar informasi publik yang dapat diakses masyarakat",
      icon: <ClipboardDocumentListIcon className="w-6 h-6" />,
      link: "/regulasi?tab=informasi-publik"
    },
    {
      title: "Regulasi",
      description: "Peraturan dan kebijakan terkait keterbukaan informasi",
      icon: <ScaleIcon className="w-6 h-6" />,
      link: "/regulasi?tab=regulasi"
    }
  ];

  return (
    <>
      <div className="flex flex-col min-h-screen">
        <Nav />
        <Hero />
        <main className="flex-grow">
          <div className="py-12 container-responsive">
            <motion.p 
              className="max-w-3xl mx-auto mb-12 text-center text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Akses informasi publik yang tersedia sesuai dengan Undang-undang Nomor 14 Tahun 2008 tentang Keterbukaan Informasi Publik
            </motion.p>

            <motion.div 
              className="flex flex-wrap justify-center gap-4 px-4"
              variants={staggerContainer}
              initial="initial"
              animate="animate"
            >
              {isLoaded ? (
                infoCards.map((card, index) => (
                  <motion.div
                    key={index}
                    className="w-full sm:w-[180px]"
                    variants={slideInUp}
                    transition={{ 
                      delay: index * 0.1,
                      duration: prefersReducedMotion ? 0 : 0.3
                    }}
                  >
                    <Link href={card.link}>
                      <motion.div 
                        className="h-full p-4 bg-white rounded-lg shadow-md"
                        whileHover={{ 
                          scale: prefersReducedMotion ? 1 : 1.05, 
                          boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" 
                        }}
                        whileTap={{ scale: prefersReducedMotion ? 1 : 0.95 }}
                      >
                        <motion.div 
                          className="flex items-center justify-center w-12 h-12 mx-auto mb-4 text-white rounded-full bg-primary-500"
                          whileHover={{ rotate: prefersReducedMotion ? 0 : 5 }}
                        >
                          {card.icon}
                        </motion.div>
                        <h2 className="mb-2 text-lg font-semibold text-center text-gray-800">{card.title}</h2>
                        <p className="text-sm text-center text-gray-600">{card.description}</p>
                      </motion.div>
                    </Link>
                  </motion.div>
                ))
              ) : (
                // Tampilkan skeleton saat loading
                <>
                  {[...Array(5)].map((_, index) => (
                    <CardSkeleton key={index} />
                  ))}
                </>
              )}
            </motion.div>
          </div>

          {/* Kalender Kegiatan dengan lazy loading */}
          <Suspense fallback={<div className="flex items-center justify-center h-96">Memuat kalender...</div>}>
            <ClientKalender />
          </Suspense>
        </main>
        {/* Footer overlay removed to avoid duplicate footer; global Footer renders in root layout for >=sm */}
      </div>
    </>
  );
}