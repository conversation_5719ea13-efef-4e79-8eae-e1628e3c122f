'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Badge } from '../../components/ui/badge';
import { formatDate } from '../../../lib/utils';
import { Search, FileText, Download, Eye, ChevronUp, ChevronDown } from 'lucide-react';
import { toast } from 'react-hot-toast';

export default function AdminPermohonanPage() {
  const [permohonanList, setPermohonanList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [multiStatus, setMultiStatus] = useState([]); // array of statuses
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [updatingId, setUpdatingId] = useState(null);
  const [selectedStatuses, setSelectedStatuses] = useState({}); // id -> status
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [sortBy, setSortBy] = useState('tanggalPermohonan');
  const [sortDir, setSortDir] = useState('desc');
  const [selectedIds, setSelectedIds] = useState(new Set());

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    diproses: 'bg-blue-100 text-blue-800',
    selesai: 'bg-green-100 text-green-800',
    ditolak: 'bg-red-100 text-red-800',
  };

  const statusLabels = {
    pending: 'Menunggu',
    diproses: 'Diproses',
    selesai: 'Selesai',
    ditolak: 'Ditolak',
  };
  const fetchPermohonan = useCallback(async () => {
    try {
      setLoading(true);
  const params = new URLSearchParams({ page: currentPage.toString(), limit: '10' });
  if (search) params.append('search', search);
  if (multiStatus.length > 0) params.append('status', multiStatus.join(','));
  if (dateFrom) params.append('dateFrom', dateFrom);
  if (dateTo) params.append('dateTo', dateTo);
  if (sortBy) params.append('sortBy', sortBy);
  if (sortDir) params.append('sortDir', sortDir);

      const response = await fetch(`/api/permohonan?${params}`);
      if (response.ok) {
        const data = await response.json();
        setPermohonanList(data.data);
        setTotalPages(data.pagination.totalPages);
        // initialize local selected statuses map for Save flow
        const map = {};
        (data.data || []).forEach((p) => { map[p.id] = p.status; });
        setSelectedStatuses(map);
      } else {
        const err = await response.json().catch(() => ({}));
        toast.error(err.error || 'Gagal memuat data');
      }
    } catch (error) {
      console.error('Error fetching permohonan:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, search, multiStatus, dateFrom, dateTo, sortBy, sortDir]);

  useEffect(() => {
    fetchPermohonan();
  }, [fetchPermohonan]);

  const updateStatus = async (id, newStatus) => {
    try {
      setUpdatingId(id);
      const response = await fetch(`/api/permohonan/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        await fetchPermohonan();
      }
    } catch (error) {
      console.error('Error updating status:', error);
    } finally {
      setUpdatingId(null);
    }
  };

  const filteredData = permohonanList; // server-side filtering & search

  const toggleSort = (field) => {
    if (sortBy === field) {
      setSortDir((prev) => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortBy(field);
      setSortDir('asc');
    }
  };

  const allChecked = filteredData.length > 0 && filteredData.every((p) => selectedIds.has(p.id));
  const toggleAll = () => {
    const next = new Set(selectedIds);
    if (allChecked) {
      filteredData.forEach((p) => next.delete(p.id));
    } else {
      filteredData.forEach((p) => next.add(p.id));
    }
    setSelectedIds(next);
  };

  const toggleOne = (id) => {
    const next = new Set(selectedIds);
    if (next.has(id)) next.delete(id); else next.add(id);
    setSelectedIds(next);
  };

  const bulkUpdate = async (newStatus) => {
    if (selectedIds.size === 0) return;
    try {
      const response = await fetch('/api/permohonan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'bulkUpdateStatus', ids: Array.from(selectedIds), status: newStatus })
      });
      if (response.ok) {
        toast.success('Status massal berhasil diupdate');
        setSelectedIds(new Set());
        await fetchPermohonan();
      } else {
        const err = await response.json().catch(() => ({}));
        toast.error(err.error || 'Gagal melakukan pembaruan massal');
      }
    } catch (e) {
      toast.error('Terjadi kesalahan saat bulk update');
    }
  };

  const exportCSV = () => {
    const headers = ['ID','Tanggal','Nama','Email','NIK','Kategori','Informasi','Status'];
    const rows = filteredData.map(p => [
      p.id,
      formatDate(p.tanggalPermohonan),
      p.namaSesuaiKtp,
      p.alamatEmail,
      p.nik,
      p.kategoriPemohon,
      (p.informasiYangDiminta || '').replace(/\n/g,' ').slice(0,500),
      p.status
    ]);
    const csv = [headers, ...rows].map(r => r.map(v => `"${String(v).replace(/"/g,'""')}"`).join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `permohonan_export_${new Date().toISOString().slice(0,10)}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-blue-800">
            Daftar Permohonan Informasi PPID
          </CardTitle>
        </CardHeader>

        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">Cari Pemohon</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Cari berdasarkan nama, NIK, atau email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-64">
              <Label>Filter Status (multi)</Label>
              <div className="flex flex-wrap gap-2">
                {['pending','diproses','selesai','ditolak'].map(s => (
                  <button
                    key={s}
                    type="button"
                    className={`px-3 py-2 rounded-md border text-sm ${multiStatus.includes(s) ? 'bg-blue-50 border-blue-300 text-blue-700' : 'bg-white border-gray-300 text-gray-700'}`}
                    onClick={() => setMultiStatus(prev => prev.includes(s) ? prev.filter(x=>x!==s) : [...prev, s])}
                  >
                    {s === 'pending' ? 'Menunggu' : s.charAt(0).toUpperCase()+s.slice(1)}
                  </button>
                ))}
              </div>
            </div>
            <div className="md:w-64">
              <Label>Rentang Tanggal</Label>
              <div className="flex gap-2">
                <Input type="date" value={dateFrom} onChange={(e)=>setDateFrom(e.target.value)} />
                <Input type="date" value={dateTo} onChange={(e)=>setDateTo(e.target.value)} />
              </div>
            </div>
            <div className="md:w-48 flex items-end">
              <Button variant="outline" onClick={exportCSV} className="w-full">
                <Download className="h-4 w-4 mr-2" /> Ekspor CSV
              </Button>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            {loading ? (
              <div className="text-center py-8">
                <p>Loading...</p>
              </div>
            ) : (
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left w-10">
                      <input type="checkbox" checked={allChecked} onChange={toggleAll} />
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-left cursor-pointer" onClick={()=>toggleSort('tanggalPermohonan')}>
                      Tanggal {sortBy==='tanggalPermohonan' && (sortDir==='asc'?<ChevronUp className="inline h-4 w-4"/>:<ChevronDown className="inline h-4 w-4"/>)}
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Nama</th>
                    <th className="border border-gray-300 px-4 py-2 text-left cursor-pointer" onClick={()=>toggleSort('namaSesuaiKtp')}>
                      NIK
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Kategori</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Informasi Diminta</th>
                    <th className="border border-gray-300 px-4 py-2 text-left cursor-pointer" onClick={()=>toggleSort('status')}>
                      Status {sortBy==='status' && (sortDir==='asc'?<ChevronUp className="inline h-4 w-4"/>:<ChevronDown className="inline h-4 w-4"/>)}
                    </th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((permohonan) => (
                    <tr key={permohonan.id} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2">
                        <input type="checkbox" checked={selectedIds.has(permohonan.id)} onChange={()=>toggleOne(permohonan.id)} />
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        {formatDate(permohonan.tanggalPermohonan)}
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div>
                          <div className="font-medium">{permohonan.namaSesuaiKtp}</div>
                          <div className="text-sm text-gray-500">{permohonan.alamatEmail}</div>
                        </div>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">{permohonan.nik}</td>
                      <td className="border border-gray-300 px-4 py-2">
                        <Badge variant="outline">
                          {permohonan.kategoriPemohon}
                        </Badge>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div className="max-w-xs">
                          <p className="truncate" title={permohonan.informasiYangDiminta}>
                            {permohonan.informasiYangDiminta}
                          </p>
                        </div>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div className="flex items-center gap-2">
                          <Badge className={statusColors[permohonan.status]}>
                            {statusLabels[permohonan.status]}
                          </Badge>
                          {/* Inline status editor with explicit save */}
                          <select
                            aria-label="Ubah status"
                            className="h-10 rounded-md border border-gray-300 bg-white px-3 text-sm min-w-[12rem]"
                            value={selectedStatuses[permohonan.id] ?? permohonan.status}
                            onChange={(e) => setSelectedStatuses((prev) => ({ ...prev, [permohonan.id]: e.target.value }))}
                            disabled={updatingId === permohonan.id}
                          >
                            <option value="pending">Menunggu</option>
                            <option value="diproses">Diproses</option>
                            <option value="selesai">Selesai</option>
                            <option value="ditolak">Ditolak</option>
                          </select>
                          <Button
                            size="sm"
                            onClick={() => updateStatus(permohonan.id, selectedStatuses[permohonan.id] ?? permohonan.status)}
                            disabled={
                              updatingId === permohonan.id ||
                              (selectedStatuses[permohonan.id] ?? permohonan.status) === permohonan.status
                            }
                            className="bg-blue-600 text-white hover:bg-blue-700"
                          >
                            Simpan
                          </Button>
                        </div>
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`/admin/permohonan/${permohonan.id}`, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {permohonan.status === 'pending' && (
                            <Button
                              size="sm"
                              onClick={() => updateStatus(permohonan.id, 'diproses')}
                            >
                              Proses
                            </Button>
                          )}
                          {permohonan.status === 'diproses' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateStatus(permohonan.id, 'selesai')}
                            >
                              Selesai
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}

            {filteredData.length === 0 && !loading && (
              <div className="text-center py-8">
                <p className="text-gray-500">Tidak ada data permohonan yang ditemukan</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => prev - 1)}
              >
                Sebelumnya
              </Button>
              <span className="flex items-center px-4">
                Halaman {currentPage} dari {totalPages}
              </span>
              <Button
                variant="outline"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => prev + 1)}
              >
                Selanjutnya
              </Button>
            </div>
          )}

          {/* Bulk actions */}
          <div className="flex flex-wrap items-center gap-2 mt-6">
            <span className="text-sm text-gray-600">Dipilih: {selectedIds.size}</span>
            <Button variant="outline" disabled={selectedIds.size===0} onClick={()=>bulkUpdate('pending')}>Set Pending</Button>
            <Button variant="outline" disabled={selectedIds.size===0} onClick={()=>bulkUpdate('diproses')}>Set Diproses</Button>
            <Button variant="outline" disabled={selectedIds.size===0} onClick={()=>bulkUpdate('selesai')}>Set Selesai</Button>
            <Button variant="outline" disabled={selectedIds.size===0} onClick={()=>bulkUpdate('ditolak')}>Set Ditolak</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
