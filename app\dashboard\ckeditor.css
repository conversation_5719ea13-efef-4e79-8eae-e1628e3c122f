/* CKEditor styles scoped to dashboard routes only */

/* Spacing and reset for CKEditor containers */
.ck-editor__editable_inline { min-height: 500px !important; padding: 0 !important; margin: 0 !important; }
.ckeditor-container { display: block !important; line-height: 0 !important; margin: 0 !important; padding: 0 !important; }

/* Reset margins/paddings */
.ck.ck-editor,
.ck.ck-editor__main,
.ck.ck-editor__editable,
.ck.ck-editor__top,
.ck.ck-toolbar,
.ck.ck-content,
.ck-editor__editable_inline { margin: 0 !important; padding: 0 !important; line-height: normal !important; }

/* Editable area padding */
.ck.ck-editor__editable { padding: 1rem !important; }

/* Form label helpers */
label[for="content"] { margin-bottom: 0 !important; display: block !important; }
.space-y-0 > * { margin-top: 0 !important; margin-bottom: 0 !important; }
div.mb-0.space-y-0 { margin-bottom: 0 !important; margin-top: 0 !important; }
.ckeditor-container::before, .ckeditor-container::after { display: none !important; content: none !important; }
form .space-y-0 + .flex { margin-top: 1rem !important; }

/* Toolbar and buttons */
.ck.ck-toolbar { padding: 0.25rem !important; border-bottom: 1px solid #c4c4c4 !important; }
.ck.ck-toolbar > .ck-toolbar__items > * { margin-top: 0 !important; margin-bottom: 0 !important; }
.ck.ck-button { margin: 0.2rem !important; }

/* Content area */
.ck.ck-content { padding: 1rem !important; min-height: 590px !important; border-radius: 0 0 0.375rem 0.375rem !important; }
.ck.ck-editor__main { border-top: 0 !important; }
.ck.ck-editor { border-radius: 0.375rem !important; overflow: hidden !important; margin-top: 0 !important; border: 0 !important; }

/* Document editor wrappers */
.editor-container__editor-wrapper { display: flex; width: 100%; max-height: var(--ckeditor5-preview-height); min-height: var(--ckeditor5-preview-height); overflow-y: auto; background: var(--ck-color-base-foreground); }
.editor-container_document-editor { border: 1px solid var(--ck-color-base-border); width: 100%; }
.editor-container_document-editor .editor-container__toolbar { display: flex; position: relative; box-shadow: 0 2px 3px hsla(0,0%,0%,0.078); }
.editor-container_document-editor .editor-container__toolbar > .ck.ck-toolbar { flex-grow: 1; width: 100%; border-bottom-right-radius: 0; border-bottom-left-radius: 0; border-top: 0; border-left: 0; border-right: 0; }
.editor-container_document-editor .editor-container__menu-bar > .ck.ck-menu-bar { border-bottom-right-radius: 0; border-bottom-left-radius: 0; border-top: 0; border-left: 0; border-right: 0; }
.editor-container_document-editor .editor-container__editor { margin-top: 28px; margin-bottom: 28px; display: flex; justify-content: center; width: 100%; }
.editor-container_document-editor .editor-container__editor .ck.ck-editor__editable { box-sizing: border-box; width: 210mm; min-height: 297mm; height: fit-content; padding: 20mm 12mm; border: 1px hsl(0,0%,82.7%) solid; background: #fff; box-shadow: 0 2px 3px hsla(0,0%,0%,0.078); margin: 0 auto; }
@media (max-width: 1300px) { .editor-container_document-editor .editor-container__editor .ck.ck-editor__editable { width: calc(100% - 40px); min-width: auto; max-width: 210mm; margin: 0 20px; } }

/* Focus and fonts */
.ck.ck-editor__editable_inline.ck-focused { border: 1px hsl(0,0%,82.7%) solid !important; box-shadow: 0 0 5px hsla(0,0%,0%,0.1) !important; }
.ck.ck-content { font-family: 'Lato', sans-serif !important; }
.ck.ck-editor__main { border: none !important; }

/* Print */
@media print { .editor-container__toolbar { display: none !important; } .editor-container__editor-wrapper { overflow: visible !important; height: auto !important; max-height: none !important; } .editor-container__editor .ck.ck-editor__editable { box-shadow: none !important; margin: 0 !important; padding: 0 !important; } }

/* Additional layout helpers */
.editor-container { margin: 0 !important; padding: 0 !important; }
.editor-container label { margin-bottom: 0 !important; padding-bottom: 0 !important; }
.editor-container .ckeditor-container { margin-top: -1px !important; }
.editor-container > label + div { margin-top: 0 !important; padding-top: 0 !important; }
.space-y-6 > .editor-container { margin-top: 1.5rem !important; }

/* Toolbar styling (clean) */
.ck.ck-toolbar { border: 1px solid #d1d5db !important; border-bottom: none !important; border-radius: 0.375rem 0.375rem 0 0 !important; background: #f9fafb !important; padding: 8px !important; }
.ck.ck-content { border: 1px solid #d1d5db !important; border-top: none !important; min-height: 300px !important; padding: 16px !important; font-size: 14px !important; line-height: 1.6 !important; }
.ck.ck-toolbar .ck-button { border-radius: 4px !important; margin: 2px !important; padding: 4px 6px !important; }
.ck.ck-toolbar .ck-button:hover { background: #e5e7eb !important; }
.ck.ck-toolbar .ck-button.ck-on { background: #dbeafe !important; color: #1d4ed8 !important; }
.ck.ck-toolbar .ck-toolbar__separator { background: #d1d5db !important; margin: 0 4px !important; width: 1px !important; height: 20px !important; }
.ck.ck-focused { border-color: #3b82f6 !important; box-shadow: 0 0 0 2px rgba(59,130,246,0.1) !important; }

/* Root scoping for dynamic editors */
.ckeditor-root { min-height: auto !important; border: none !important; margin: 0 !important; padding: 0 !important; }
.ckeditor-root .ck.ck-editor { border: none !important; box-shadow: none !important; margin: 0 !important; padding: 0 !important; }
.ckeditor-root .ck.ck-toolbar { border: none !important; border-bottom: 1px solid #e5e7eb !important; box-shadow: none !important; background: transparent !important; padding: 0.5rem 0 !important; margin: 0 !important; }
.ckeditor-root .ck.ck-toolbar > .ck-toolbar__items > * { margin: 0 2px !important; }
.ckeditor-root .ck.ck-content { min-height: 590px !important; border: none !important; box-shadow: none !important; padding: 1rem 0 !important; margin: 0 !important; background: white !important; }
.ckeditor-root .ck.ck-editor__editable.ck-focused { border: none !important; box-shadow: none !important; outline: none !important; }
.ckeditor-root .ck.ck-editor__main { border: none !important; }
.ckeditor-root .dynamic-editor-content { min-height: 590px !important; }

/* Document-style editor container */
.main-container { --ckeditor5-preview-height: 700px; font-family: 'Lato', sans-serif; width: 100%; margin-left: auto; margin-right: auto; position: relative; }
