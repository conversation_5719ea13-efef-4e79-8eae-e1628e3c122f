'use client'
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

export default function Regulasi() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('Informasi Berkala');
  const [tagGroups, setTagGroups] = useState({});

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const response = await fetch('/api/posts');
        if (!response.ok) {
          throw new Error('Gagal mengambil data');
        }
        const data = await response.json();
        
        // Filter hanya post yang sudah dipublikasikan
        const publishedPosts = data.posts.filter(post => post.published);
        
        // Kelompokkan post berdasarkan tag
        const groups = {};
        publishedPosts.forEach(post => {
          post.tags.forEach(tag => {
            if (!groups[tag.name]) {
              groups[tag.name] = [];
            }
            groups[tag.name].push(post);
          });
        });
        
        setTagGroups(groups);
        setPosts(publishedPosts);
        
        // Set tab aktif ke "Informasi Berkala" jika ada
        if (groups['Informasi Berkala']) {
          setActiveTab('Informasi Berkala');
        } else if (Object.keys(groups).length > 0) {
          setActiveTab(Object.keys(groups)[0]);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-500">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">Regulasi</h1>
      
      {/* Tab Navigation - horizontal scroll on mobile, wrap on md+ */}
      <div
        className="flex gap-2 mb-8 overflow-x-auto flex-nowrap md:flex-wrap justify-start md:justify-center"
        role="tablist"
        aria-label="Kategori regulasi"
      >
        {Object.keys(tagGroups).map((tagName) => (
          <button
            key={tagName}
            onClick={() => setActiveTab(tagName)}
            role="tab"
            aria-selected={activeTab === tagName}
            className={`shrink-0 px-4 py-2 rounded-lg transition-colors border ${
              activeTab === tagName
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'
            }`}
          >
            {tagName}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tagGroups[activeTab]?.map((post) => (
          <div key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            {post.featuredImage && (
              <div className="relative h-48">
                <Image
                  src={post.featuredImage}
                  alt={post.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  quality={70}
                  className="object-cover"
                />
              </div>
            )}
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-2">
                <Link href={`/regulasi/${post.slug}`} className="hover:text-blue-600 transition-colors">
                  {post.title}
                </Link>
              </h2>
              <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{post.author?.name || 'Admin'}</span>
                <span>
                  {format(new Date(post.publishedAt), 'dd MMMM yyyy', { locale: id })}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {(!tagGroups[activeTab] || tagGroups[activeTab].length === 0) && (
        <div className="text-center text-gray-500 mt-8">
          Belum ada post dalam kategori ini
        </div>
      )}
    </div>
  );
}
