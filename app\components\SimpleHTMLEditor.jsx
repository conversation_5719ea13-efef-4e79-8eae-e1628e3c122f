'use client';

import { useState, useEffect, useRef } from 'react';

let CKEditor = null;
let ClassicEditor = null;

export default function SimpleHTMLEditor({ 
  data = '', 
  onChange, 
  config = {},
  height = 600 
}) {
  const [editorLoaded, setEditorLoaded] = useState(false);
  const [content, setContent] = useState(data);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('visual'); // 'visual' or 'source'
  const [sourceCode, setSourceCode] = useState('');
  const [accordionWarning, setAccordionWarning] = useState(false);
  const [showAccordionModal, setShowAccordionModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [accordionType, setAccordionType] = useState('info');
  const [showHTMLModal, setShowHTMLModal] = useState(false);
  const [htmlCode, setHtmlCode] = useState('');
  const editorRef = useRef(null);

  useEffect(() => {
    setContent(data);
    setSourceCode(data);
  }, [data]);

  // Auto-switch to source mode if content contains accordion
  useEffect(() => {
    if (data && data.includes('<details')) {
      setActiveTab('source');
      setSourceCode(data);
    }
  }, [data]);

  useEffect(() => {
    let isMounted = true;

    const loadCKEditor = async () => {
      try {
        if (typeof window === 'undefined') return;

        if (!CKEditor || !ClassicEditor) {
          const [ckeditorReact, classicEditorModule] = await Promise.all([
            import('@ckeditor/ckeditor5-react'),
            import('@ckeditor/ckeditor5-build-classic')
          ]);

          CKEditor = ckeditorReact.CKEditor;
          ClassicEditor = classicEditorModule.default;
        }

        if (isMounted) {
          setEditorLoaded(true);
        }

      } catch (err) {
        console.error('CKEditor loading error:', err);
        if (isMounted) {
          setError(err.message);
        }
      }
    };

    loadCKEditor();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleEditorChange = (event, editor) => {
    const data = editor.getData();
    setContent(data);
    
    // Update source code juga untuk sinkronisasi
    setSourceCode(data);
    
    if (onChange) {
      onChange(data);
    }
  };

  const switchToVisual = () => {
    if (activeTab === 'source') {
      // Check if source contains accordion before switching
      if (sourceCode.includes('<details')) {
        // Show confirmation modal instead of preventing switch
        setShowConfirmModal(true);
        return;
      }
      
      setContent(sourceCode);
      if (onChange) {
        onChange(sourceCode);
      }
    }
    setActiveTab('visual');
  };

  const confirmSwitchToVisual = () => {
    // User confirms they want to switch despite accordion loss
    setShowConfirmModal(false);
    setContent(sourceCode);
    if (onChange) {
      onChange(sourceCode);
    }
    setActiveTab('visual');
  };

  const cancelSwitchToVisual = () => {
    setShowConfirmModal(false);
  };

  const switchToSource = () => {
    if (activeTab === 'visual' && editorRef.current) {
      const currentContent = editorRef.current.getData();
      setSourceCode(currentContent);
    }
    setActiveTab('source');
  };

  const handleSourceCodeChange = (e) => {
    const newCode = e.target.value;
    setSourceCode(newCode);
    setContent(newCode);
    if (onChange) {
      onChange(newCode);
    }
  };

  const insertHTML = () => {
    if (editorRef.current && htmlCode.trim()) {
      const editor = editorRef.current;
      const viewFragment = editor.data.processor.toView(htmlCode);
      const modelFragment = editor.data.toModel(viewFragment);
      
      editor.model.insertContent(modelFragment);
      setHtmlCode('');
      setShowHTMLModal(false);
    }
  };

  const insertAccordion = () => {
    if (editorRef.current) {
      const editor = editorRef.current;
      const accordionHTML = `
<details class="border rounded-md p-4 my-3 bg-blue-50 border-blue-200">
  <summary class="cursor-pointer font-semibold text-blue-600 mb-2">
    📋 Klik untuk membuka accordion
  </summary>
  <div class="mt-2 text-gray-700">
    <p>Masukkan konten accordion Anda di sini...</p>
    <p>Anda dapat menambahkan teks, daftar, atau elemen HTML lainnya.</p>
  </div>
</details>`;
      
      const viewFragment = editor.data.processor.toView(accordionHTML);
      const modelFragment = editor.data.toModel(viewFragment);
      
      editor.model.insertContent(modelFragment);
    }
  };

  const getAccordionTemplate = (type) => {
    const templates = {
      info: {
        html: `
<details class="border rounded-md p-4 my-3 bg-blue-50 border-blue-200">
  <summary class="cursor-pointer font-semibold text-blue-600 mb-2">
    📋 Informasi
  </summary>
  <div class="mt-2 text-gray-700">
    <p>Masukkan informasi penting di sini...</p>
  </div>
</details>`,
        name: 'Info (Biru)'
      },
      warning: {
        html: `
<details class="border rounded-md p-4 my-3 bg-yellow-50 border-yellow-200">
  <summary class="cursor-pointer font-semibold text-yellow-600 mb-2">
    ⚠️ Peringatan
  </summary>
  <div class="mt-2 text-gray-700">
    <p>Masukkan peringatan atau catatan penting di sini...</p>
  </div>
</details>`,
        name: 'Warning (Kuning)'
      },
      success: {
        html: `
<details class="border rounded-md p-4 my-3 bg-green-50 border-green-200">
  <summary class="cursor-pointer font-semibold text-green-600 mb-2">
    ✅ Berhasil
  </summary>
  <div class="mt-2 text-gray-700">
    <p>Masukkan konfirmasi atau informasi sukses di sini...</p>
  </div>
</details>`,
        name: 'Success (Hijau)'
      },
      faq: {
        html: `
<details class="border rounded-md p-4 my-3 bg-purple-50 border-purple-200">
  <summary class="cursor-pointer font-semibold text-purple-600 mb-2">
    ❓ Pertanyaan Umum
  </summary>
  <div class="mt-2 text-gray-700">
    <p><strong>Pertanyaan:</strong> Masukkan pertanyaan di sini</p>
    <p><strong>Jawaban:</strong> Masukkan jawaban di sini</p>
  </div>
</details>`,
        name: 'FAQ (Ungu)'
      }
    };
    return templates[type] || templates.info;
  };

  const insertAccordionTemplate = () => {
    if (editorRef.current) {
      const template = getAccordionTemplate(accordionType);
      
      // Get current content from editor
      const currentContent = editorRef.current.getData();
      
      // Combine with accordion template
      const newContent = currentContent + template.html;
      
      // Update all states
      setContent(newContent);
      setSourceCode(newContent);
      if (onChange) {
        onChange(newContent);
      }
      
      // Set data to editor (CKEditor will handle what it can)
      editorRef.current.setData(newContent);
      
      setShowAccordionModal(false);
      
      // Show notification that accordion was added
      console.log('Accordion template inserted. Switch to Source tab to see raw HTML.');
    }
  };



  const editorConfig = {
    licenseKey: 'GPL',
    toolbar: [
      'heading',
      '|',
      'bold',
      'italic', 
      'link',
      '|',
      'bulletedList',
      'numberedList',
      '|',
      'undo',
      'redo'
    ],
    placeholder: 'Mulai menulis konten di sini...',
    language: 'id',
    // Coba dengan General HTML Support
    htmlEmbed: {
      showPreviews: true
    },
    // Pastikan tidak ada filtering yang terlalu ketat
    heading: {
      options: [
        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
      ]
    },
    ...config
  };

  if (!editorLoaded) {
    return (
      <div 
        style={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          backgroundColor: '#f9fafb',
          minHeight: `${height}px`
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '32px',
            height: '32px',
            border: '2px solid #d1d5db',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 8px'
          }}></div>
          <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading Editor...</span>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '16px', border: '1px solid #fca5a5', borderRadius: '6px', backgroundColor: '#fef2f2' }}>
        <p style={{ color: '#dc2626', marginBottom: '8px', fontSize: '14px' }}>
          Failed to load Editor: {error}
        </p>
        <textarea
          value={content}
          onChange={(e) => {
            setContent(e.target.value);
            if (onChange) onChange(e.target.value);
          }}
          placeholder="Mulai menulis konten di sini..."
          style={{
            width: '100%',
            height: `${height - 50}px`,
            padding: '12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px',
            lineHeight: '1.5',
            resize: 'vertical',
            fontFamily: 'inherit'
          }}
        />
      </div>
    );
  }

  if (editorLoaded && CKEditor && ClassicEditor) {
    return (
      <div style={{ position: 'relative' }}>
        <style jsx global>{`
          .ck-editor__editable_inline {
            min-height: ${height - 80}px !important;
          }
          .ck-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            font-size: 14px;
            line-height: 1.6;
          }
          .ck-content.unified-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
            font-size: 14px !important;
            line-height: 1.6 !important;
          }
          .ck-toolbar {
            border-color: #d1d5db;
            background: #f9fafb;
            border-radius: 6px 6px 0 0;
          }
          .ck-editor__editable {
            border-color: #d1d5db;
            border-radius: 0 0 6px 6px;
          }
          .ck-focused {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
          }
        `}</style>
        
        {/* Tab Navigation */}
        <div className="mb-3 border-b border-gray-200">
          <nav className="flex space-x-0">
            <button
              type="button"
              onClick={switchToVisual}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'visual'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              👁️ Visual
            </button>
            <button
              type="button"
              onClick={switchToSource}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'source'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📝 Source
            </button>
          </nav>
        </div>
        
        {/* Action Buttons - Only show in Visual mode */}
        {activeTab === 'visual' && (
          <div className="flex items-center justify-end gap-2 mb-3">
            {/* Insert Accordion Button */}
            <button
              type="button"
              onClick={() => setShowAccordionModal(true)}
              className="flex items-center gap-1 px-3 py-1 text-xs text-white transition-colors bg-blue-600 rounded hover:bg-blue-700"
              title="Insert Accordion Template"
            >
              📁 Insert Accordion
            </button>
            
            {/* HTML Embed Button */}
            <button
              type="button"
              onClick={() => setShowHTMLModal(true)}
              className="flex items-center gap-1 px-3 py-1 text-xs text-white transition-colors bg-green-600 rounded hover:bg-green-700"
              title="Insert Custom HTML"
            >
              &lt;/&gt; HTML Embed
            </button>
          </div>
        )}
        
        {/* Editor Content */}
        {activeTab === 'visual' ? (
          <CKEditor
            editor={ClassicEditor}
            data={content}
            config={editorConfig}
            onChange={handleEditorChange}
            onReady={(editor) => {
              editorRef.current = editor;
              
              // Set initial content
              const initialContent = data || content;
              if (initialContent) {
                editor.setData(initialContent);
                setSourceCode(initialContent);
              }
              
              // Monitor data changes to preserve accordion
              editor.model.document.on('change:data', () => {
                if (activeTab === 'visual') {
                  const editorData = editor.getData();
                  
                  // Check if accordion content was lost during editing
                  if (sourceCode.includes('<details') && !editorData.includes('<details')) {
                    // Show warning that accordion might be lost
                    setAccordionWarning(true);
                    setTimeout(() => setAccordionWarning(false), 8000);
                    
                    console.warn('Accordion content detected. Consider using Source mode for editing accordion content.');
                  }
                  
                  setSourceCode(editorData);
                }
              });
            }}
            onError={(error) => {
              console.error('CKEditor error:', error);
              setError(error.message);
            }}
          />
        ) : (
          <div className="border border-gray-300 rounded-md">
            <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
              <span className="text-xs text-gray-600">💡 Edit HTML source code directly</span>
            </div>
            <textarea
              value={sourceCode}
              onChange={handleSourceCodeChange}
              className="w-full p-3 font-mono text-sm border-0 resize-none rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{ height: `${height - 80}px` }}
              placeholder="<p>Masukkan HTML code di sini...</p>"
            />
          </div>
        )}
        
        {/* HTML Embed Modal */}
        {showHTMLModal && (
          <div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
            onClick={(e) => {
              // Only close if clicking the backdrop, not the modal content
              if (e.target === e.currentTarget) {
                setShowHTMLModal(false);
              }
            }}
          >
            <div className="w-full max-w-2xl p-6 mx-4 bg-white rounded-lg" onClick={(e) => e.stopPropagation()}>
              <h3 className="mb-4 text-lg font-semibold">HTML Embed</h3>
              
              <textarea
                value={htmlCode}
                onChange={(e) => setHtmlCode(e.target.value)}
                placeholder="Masukkan kode HTML di sini..."
                className="w-full h-64 p-3 font-mono text-sm border border-gray-300 rounded-md"
              />
              
              <div className="flex justify-between mt-4">
                <div className="text-xs text-gray-500">
                  <p>💡 Tip: Masukkan HTML, iframe embed, atau script</p>
                </div>
                
                <div className="space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowHTMLModal(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={insertHTML}
                    className="px-4 py-2 text-white bg-green-600 rounded hover:bg-green-700"
                  >
                    Insert HTML
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Accordion Templates Modal */}
        {showAccordionModal && (
          <div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowAccordionModal(false);
              }
            }}
          >
            <div className="w-full max-w-3xl p-6 mx-4 bg-white rounded-lg" onClick={(e) => e.stopPropagation()}>
              <h3 className="mb-4 text-lg font-semibold">📁 Insert Accordion Template</h3>
              
              <div className="mb-4">
                <label className="block mb-2 text-sm font-medium text-gray-700">
                  Pilih Template Accordion:
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {Object.entries({
                    info: 'Info (Biru)',
                    warning: 'Warning (Kuning)', 
                    success: 'Success (Hijau)',
                    faq: 'FAQ (Ungu)'
                  }).map(([key, label]) => (
                    <button
                      key={key}
                      type="button"
                      onClick={() => setAccordionType(key)}
                      className={`p-3 text-left border rounded-md transition-colors ${
                        accordionType === key
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="font-medium">{label}</div>
                      <div className="mt-1 text-xs text-gray-500">
                        {key === 'info' && '📋 Untuk informasi umum'}
                        {key === 'warning' && '⚠️ Untuk peringatan'}
                        {key === 'success' && '✅ Untuk konfirmasi'}
                        {key === 'faq' && '❓ Untuk tanya jawab'}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="p-4 mb-4 rounded-md bg-gray-50">
                <div className="mb-2 text-sm font-medium text-gray-700">Preview:</div>
                <div 
                  className="text-sm"
                  dangerouslySetInnerHTML={{ 
                    __html: getAccordionTemplate(accordionType).html.trim()
                  }}
                />
              </div>
              
              <div className="flex justify-between">
                <div className="text-xs text-gray-500">
                  <p>💡 Template akan dimasukkan ke editor dan dapat diedit langsung</p>
                  <p>📝 Accordion akan bekerja dengan sempurna saat konten di-render</p>
                </div>
                
                <div className="space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowAccordionModal(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={insertAccordionTemplate}
                    className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700"
                  >
                    Insert Accordion
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Modal for Visual Mode Switch */}
        {showConfirmModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <span className="text-orange-500 mr-3 text-2xl">⚠️</span>
                <h3 className="text-lg font-semibold text-gray-900">Konfirmasi Beralih ke Visual Mode</h3>
              </div>
              
              <div className="mb-6">
                <p className="text-gray-700 mb-3">
                  Konten ini mengandung <strong>accordion</strong> yang akan <span className="text-red-600 font-semibold">hilang</span> jika beralih ke Visual Mode.
                </p>
                <div className="bg-red-50 border border-red-200 rounded p-3 mb-3">
                  <p className="text-red-800 text-sm font-medium">⚠️ Yang akan hilang:</p>
                  <ul className="text-red-700 text-sm mt-1 ml-4 list-disc">
                    <li>Interactive accordion functionality</li>
                    <li>Click to expand/collapse</li>
                    <li>Accordion styling & struktur</li>
                  </ul>
                </div>
                <p className="text-gray-600 text-sm">
                  <strong>Rekomendasi:</strong> Tetap di Source Mode untuk preservasi accordion.
                </p>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={cancelSwitchToVisual}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
                >
                  Tetap di Source Mode
                </button>
                <button
                  type="button"
                  onClick={confirmSwitchToVisual}
                  className="px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700"
                >
                  Lanjut ke Visual Mode
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
}
