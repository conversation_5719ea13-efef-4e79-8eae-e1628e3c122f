import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../../lib/auth';
import { hash } from 'bcryptjs';

// GET /api/users/[id] - Mendapatkan pengguna berdasarkan ID
export async function GET(request, props) {
  const params = await props.params;
  try {
    // Verify authentication using JWT token
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    let currentUser;
    try {
      currentUser = await verifyToken(token);
    } catch (verifyError) {
      console.error('Token verification error:', verifyError);
      return NextResponse.json(
        { error: 'Token tidak valid' },
        { status: 401 }
      );
    }
    
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Token tidak valid atau kadaluwarsa' },
        { status: 401 }
      );
    }
    
    const { id } = params;
  // Only allow users to access their own data unless they're an admin
  if (currentUser.id !== id && String(currentUser.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    
    if (!user) {
      return NextResponse.json(
        { error: 'Pengguna tidak ditemukan' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ user });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data pengguna' },
      { status: 500 }
    );
  }
}

// PATCH /api/users/[id] - Memperbarui pengguna
export async function PATCH(request, props) {
  const params = await props.params;
  try {
    // Verify authentication using JWT token
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    let currentUser;
    try {
      currentUser = await verifyToken(token);
    } catch (verifyError) {
      console.error('Token verification error:', verifyError);
      return NextResponse.json(
        { error: 'Token tidak valid' },
        { status: 401 }
      );
    }
    
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Token tidak valid atau kadaluwarsa' },
        { status: 401 }
      );
    }
    
    const { id } = params;
    
  // Only allow users to update their own data unless they're an admin
  if (currentUser.id !== id && String(currentUser.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    
    const data = await request.json();
    
    // Validate data
    if (data.email && !data.email.includes('@')) {
      return NextResponse.json(
        { error: 'Email tidak valid' },
        { status: 400 }
      );
    }
    
    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        username: data.username,
        email: data.email,
      },
    });
    
    return NextResponse.json({ 
      success: true,
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
      }
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui pengguna' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Menghapus pengguna (admin only)
export async function DELETE(request, props) {
  const params = await props.params;
  try {
    // Verify auth
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let currentUser;
    try {
      currentUser = await verifyToken(token);
    } catch (verifyError) {
      console.error('Token verification error:', verifyError);
      return NextResponse.json({ error: 'Token tidak valid' }, { status: 401 });
    }

    if (!currentUser) {
      return NextResponse.json({ error: 'Token tidak valid atau kadaluwarsa' }, { status: 401 });
    }

    // Admin only
    if (String(currentUser.role || '').toLowerCase() !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = params;

    // Optional: prevent deleting self to avoid lockout
    if (currentUser.id === id) {
      return NextResponse.json({ error: 'Tidak dapat menghapus akun sendiri' }, { status: 400 });
    }

    // Ensure user exists
    const existing = await prisma.user.findUnique({ where: { id } });
    if (!existing) {
      return NextResponse.json({ error: 'Pengguna tidak ditemukan' }, { status: 404 });
    }

    await prisma.user.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    // Common issue: foreign key constraints. Bubble up a friendly message.
    return NextResponse.json(
      { error: 'Gagal menghapus pengguna. Pastikan tidak ada data terkait.' },
      { status: 400 }
    );
  }
}
