import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function GET(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        tagsonposts: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            username: true,
          },
        },
      },
    });
    
    if (!post) {
      return NextResponse.json(
        { error: 'Postingan tidak ditemukan' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(post);
  } catch (error) {
    console.error('Error fetching post:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil data postingan' },
      { status: 500 }
    );
  }
}

export async function PUT(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    const data = await request.json();
    const { title, slug, content, excerpt, featuredImage, published, tags } = data;
    
    // Debug log to check content format
    console.log('Content received by API (first 300 chars):', content ? content.substring(0, 300) : 'No content');
    console.log('Content contains encoded HTML:', content && (content.includes('&lt;') || content.includes('&gt;')));
    
    // Update post
    const post = await prisma.post.update({
      where: { id },
      data: {
        title,
        slug,
        content,
        excerpt,
        featuredImage,
        published,
        publishedAt: published ? (data.publishedAt || new Date()) : null,
        updatedAt: new Date(), // Manually set updatedAt since @updatedAt is missing in schema
      },
    });
    
    // Update tags if provided
    if (tags) {
      // Delete existing tag connections
      await prisma.tagsonposts.deleteMany({
        where: { postId: id },
      });

      // Create new tag connections
      if (tags.length > 0) {
        const tagConnections = tags.map(tagId => ({
          postId: post.id,
          tagId,
        }));

        await prisma.tagsonposts.createMany({
          data: tagConnections,
        });
      }
    }
    
    return NextResponse.json({ success: true, post });
  } catch (error) {
    console.error('Error updating post:', error);
    return NextResponse.json(
      { success: false, error: 'Gagal memperbarui postingan' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    // Delete tag connections first - perbaikan nama model
    await prisma.tagsonposts.deleteMany({
      where: { postId: id },
    });
    
    // Delete post
    await prisma.post.delete({
      where: { id },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting post:', error);
    return NextResponse.json(
      { success: false, error: 'Gagal menghapus postingan: ' + error.message },
      { status: 500 }
    );
  }
}


