# Panduan Fitur Modal Berbasis URL

Dokumen ini menjelaskan cara membuat, menampilkan, dan menyematkan Modal yang dipanggil via parameter URL, serta opsi styling konten.

## Konsep Singkat
- Modal dapat dibuka dengan menambahkan query string pada URL: `?modal=stored&modalData=<data>`
- `stored` akan menampilkan konten yang disimpan di database (id modal di-encode di `modalData`).
- Aplikasi akan tetap berada di halaman saat ini; hanya query yang berubah.

## Membuat Modal Baru
1. Buka Dashboard → Konten → Buat Modal (Baru) (`/dashboard/urlmodals/new`).
2. Isi:
   - Judul: untuk identifikasi di daftar admin (tidak ditampilkan di modal).
   - Konten: HTML diizinkan dan disanitasi (lihat “Styling”).
3. Simpan. Anda akan diarahkan ke daftar modal.

## Menampilkan/Embed Modal di Halaman
- Dari daftar modal (`/dashboard/urlmodals`) salin link relatif yang berupa query: `?modal=stored&modalData=...`
- Tempelkan link tersebut ke mana saja (mis. tautan di konten halaman):
  ```html
  <a href="?modal=stored&modalData=...">Buka Modal</a>
  ```
- Saat diklik, modal akan terbuka tanpa pindah halaman.

## Styling Konten (Rata Tengah)
Sanitizer mengizinkan kelas Tailwind tertentu. Untuk rata tengah:
- Bungkus elemen dengan kelas `text-center` pada tag berikut: `p`, `div`, `h1`–`h4`.
- Contoh:
  ```html
  <div class="text-center">
    <h3>Judul Konten</h3>
    <p>Deskripsi yang diratakan ke tengah.</p>
  </div>
  ```
Catatan: Inline style seperti `style="text-align:center"` tidak disimpan. Pakai utilitas Tailwind `text-center`.

## Menyisipkan Gambar di Konten Modal
Gambar diizinkan oleh sanitizer. Gunakan tag `<img>` dengan atribut dan kelas berikut agar responsif:

- Contoh HTML:
  ```html
  <img src="/uploads/contoh.jpg" alt="Ilustrasi" class="mx-auto w-full h-auto rounded" loading="lazy" />
  ```
- Atribut yang didukung: `src`, `alt`, `title`, `width`, `height`, `loading`, `decoding`, `class`.
- Kelas Tailwind yang direkomendasikan: `w-full h-auto mx-auto rounded max-w-full object-contain`.
- Pastikan file dapat diakses:
  - Letakkan di `public/` untuk path seperti `/gambar.jpg`, atau
  - Gunakan folder upload yang diizinkan (`NEXT_PUBLIC_UPLOAD_PATH`, default `/uploads`).
  - Jika memuat dari domain eksternal, pastikan domain ada di `NEXT_PUBLIC_ALLOWED_IMAGE_DOMAINS` dan di `next.config.mjs` (images.domains).

## Tautan (Link) Tanpa Garis Bawah
Untuk menghilangkan underline pada tautan, gunakan kelas Tailwind berikut pada elemen `<a>`:

```html
<a href="?modal=stored&modalData=..." class="no-underline hover:no-underline text-blue-600 transition-colors">Buka Modal</a>
```

Sanitizer mengizinkan kelas: `no-underline`, `hover:no-underline`, `text-blue-600`, serta atribut `href`, `title`, `target`, `rel`.

## Judul Modal (UI)
- Judul modal tidak ditampilkan di UI. Tetap ada label aksesibilitas tersembunyi untuk screen reader.
- Field Judul di form hanya untuk identifikasi di dashboard (daftar modal).

## Ukuran Modal
Anda bisa mengatur ukuran via `size` pada `modalData`:
- Nilai: `sm | md | lg | xl | 2xl | full` (default `lg`).
- Contoh URL: `?modal=stored&modalData=%7B%22id%22%3A1%2C%22size%22%3A%22xl%22%7D`
- Mapping lebar responsif:
  - sm → `sm:max-w-sm md:max-w-md lg:max-w-lg`
  - md → `sm:max-w-md md:max-w-lg lg:max-w-xl`
  - lg → `sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl`
  - xl → `sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl`
  - 2xl → `sm:max-w-2xl md:max-w-3xl lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl`
  - full → `w-screen h-screen max-w-none`

Konten panjang otomatis scroll (`overflow-y-auto`).

## Komponen Terkait
- Host modal global: `app/ClientWrapper.js` (membungkus host dengan Suspense).
- Modal tersimpan: `app/components/url-modals/StoredModal.jsx` (menarik konten dari `/api/urlmodals/[id]`).
- Renderer konten aman: `app/components/SafeContentRenderer.jsx` (sanitasi + intercept link `?modal=...`).

## Troubleshooting
- Tidak bisa menyimpan/ambil modal (500): pastikan schema Prisma sudah diterapkan:
  - Jalankan db push/generate lalu restart dev server.
- Link tidak membuka modal: pastikan href berisi `?modal=stored&modalData=...` dan bukan URL absolut ke domain lain.

## Catatan Teknis
- Komponen yang memakai `useSearchParams` sudah dibungkus `Suspense` untuk menghindari peringatan Next.js.
- Sanitizer hanya mengizinkan subset tag/atribut/kelas Tailwind. Tambahkan kelas yang diperlukan di `SafeContentRenderer` bila dibutuhkan.
