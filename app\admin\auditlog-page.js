"use client";
import { useEffect, useState } from "react";

export default function AuditLogAdminPage() {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch("/api/auditlog")
      .then((res) => res.json())
      .then((data) => {
        setLogs(data.logs || []);
        setLoading(false);
      })
      .catch((e) => {
        setError("Gagal memuat data audit log");
        setLoading(false);
      });
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Riwayat Audit (Perubahan Status Permohonan & Keberatan)</h1>
      {loading && <div>Memuat...</div>}
      {error && <div className="text-red-500">{error}</div>}
      {!loading && !error && (
        <div className="overflow-x-auto">
          <table className="min-w-full border text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-2 py-1">Waktu</th>
                <th className="border px-2 py-1">User</th>
                <th className="border px-2 py-1">Entitas</th>
                <th className="border px-2 py-1">ID</th>
                <th className="border px-2 py-1">Aksi</th>
                <th className="border px-2 py-1">Field</th>
                <th className="border px-2 py-1">Dari</th>
                <th className="border px-2 py-1">Ke</th>
              </tr>
            </thead>
            <tbody>
              {logs.length === 0 && (
                <tr><td colSpan={8} className="text-center py-4">Tidak ada data audit log</td></tr>
              )}
              {logs.map((log) => (
                <tr key={log.id}>
                  <td className="border px-2 py-1 whitespace-nowrap">{new Date(log.createdAt).toLocaleString()}</td>
                  <td className="border px-2 py-1">{log.userId}</td>
                  <td className="border px-2 py-1">{log.entityType}</td>
                  <td className="border px-2 py-1">{log.entityId}</td>
                  <td className="border px-2 py-1">{log.action}</td>
                  <td className="border px-2 py-1">{log.field}</td>
                  <td className="border px-2 py-1">{log.oldValue}</td>
                  <td className="border px-2 py-1">{log.newValue}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
