'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAccessibility } from '../context/AccessibilityContext';
import { 
  Volume2, VolumeX, 
  ZoomIn, 
  Eye, 
  Minus, 
  Highlighter, 
  BookOpen,
  Settings,
  X,
  FileAudio,
  LineChart,
  Accessibility
} from 'lucide-react';

export default function AccessibilityControls() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('');
  const [pageDescription, setPageDescription] = useState('');
  const { settings, toggleSetting, isSpeaking, speak, stopSpeaking } = useAccessibility();
  const menuRef = useRef(null);
  const buttonRef = useRef(null);

  // Ensure client-only render to avoid hydration mismatches
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  // Detect current page for auto description - dengan optimasi
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      setCurrentPage(path);
      
      // Define descriptions for common pages
      const descriptions = {
        '/': 'Halaman utama PPID BPMP Provinsi Kalimantan Timur.',
        '/profil': 'Halaman profil PPID BPMP Provinsi Kalimantan Timur. Berisi informasi tentang profil dan struktur organisasi.',
        '/informasi': 'Halaman informasi publik PPID BPMP Provinsi Kalimantan Timur.',
        '/permohonan': 'Halaman permohonan informasi PPID BPMP Provinsi Kalimantan Timur.',
        '/login': 'Halaman login untuk administrator PPID BPMP Provinsi Kalimantan Timur.',
        '/register': 'Halaman pendaftaran akun PPID BPMP Provinsi Kalimantan Timur.',
        '/dashboard': 'Dasbor administrator PPID BPMP Provinsi Kalimantan Timur.',
        '/regulasi': 'Halaman regulasi PPID BPMP Provinsi Kalimantan Timur.',
        '/tautan': 'Halaman tautan penting PPID BPMP Provinsi Kalimantan Timur.',
      };
      
      // Set description based on current path dengan fallback yang lebih baik
      const desc = descriptions[path] || `Halaman ${path.replace(/\//g, ' ').trim() || 'utama'} PPID BPMP Provinsi Kalimantan Timur.`;
      setPageDescription(desc);
    }
  }, []);

  // Manajemen focus - trap focus inside menu when open
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;
      
      // Escape closes the menu
      if (e.key === 'Escape') {
        setIsOpen(false);
        buttonRef.current?.focus();
      }
      
      // Trap focus inside the menu
      if (e.key === 'Tab' && menuRef.current) {
        const focusableElements = menuRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);
  // Gunakan useCallback untuk memperforma fungsi yang akan digunakan dalam event listeners
  const handleHoverIn = useCallback((e) => {
    if (!e || !e.target) return;
    
    // Make sure we have a valid DOM element and it has the attribute
    if (e.target instanceof Element && 
        e.target.hasAttribute && 
        e.target.hasAttribute('data-tts-enabled')) {
      
      // Get the readable text with fallbacks
      const textToRead = e.target.getAttribute('aria-label') || 
                          e.target.title || 
                          e.target.textContent?.trim();
      
      // Only read if there's meaningful text
      if (textToRead && textToRead.length > 2) {
        // Use a unique ID if available or create a temporary one
        const elementId = e.target.id || `tts-elem-${Math.floor(Math.random() * 10000)}`;
        
        // Set temporary ID if needed
        if (!e.target.id) {
          e.target.id = elementId;
        }
        
        speak(textToRead, elementId);
      }
    }
  }, [speak]);
  // Add event listeners for TTS on hover dengan debounce
  useEffect(() => {
    if (settings.textToSpeech && typeof window !== 'undefined') {
      // Simple debounce to prevent rapid-fire speech events
      let hoverTimer;
      
      const handleMouseEnter = (e) => {
        // Clear any existing timer
        clearTimeout(hoverTimer);
        
        // Set a small delay before speaking to avoid speaking during rapid movements
        hoverTimer = setTimeout(() => {
          handleHoverIn(e);
        }, 200); // 200ms delay
      };
      
      const handleMouseLeave = () => {
        // Clear the timer when mouse leaves the element
        clearTimeout(hoverTimer);
      };
      
      // Using event delegation for better performance
      document.body.addEventListener('mouseenter', handleMouseEnter, true);
      document.body.addEventListener('mouseleave', handleMouseLeave, true);
      
      // Cleanup
      return () => {
        document.body.removeEventListener('mouseenter', handleMouseEnter, true);
        document.body.removeEventListener('mouseleave', handleMouseLeave, true);
        clearTimeout(hoverTimer);
      };
    }
  }, [settings.textToSpeech, handleHoverIn]);

  // Mute animasi jika reducedMotion diaktifkan
  const animationConfig = settings.reducedMotion
    ? { initial: {}, animate: {}, exit: {} }
    : {
        initial: { opacity: 0, y: 50 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: 50 }
      };  // Fungsi untuk membaca halaman saat ini
  const handleReadPage = () => {
    // Find the main content or fallback to document
    const mainContent = document.querySelector('main') || document.querySelector('#main-content');
    
    // Get the title of the page - try multiple sources
    const h1 = document.querySelector('h1');
    const title = h1 ? h1.textContent : document.title;
    
    // Collect readable paragraphs for better context
    let contentText = '';
    if (mainContent) {
      // Get the first few paragraphs for a summary
      const paragraphs = mainContent.querySelectorAll('p');
      const maxParagraphs = Math.min(3, paragraphs.length);
      
      for (let i = 0; i < maxParagraphs; i++) {
        contentText += paragraphs[i].textContent + ' ';
      }
    }
    
    // Check if browser is actively reading before starting a new speech
    if (window.speechSynthesis && window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
      // Small delay to ensure speech is fully canceled
      setTimeout(() => {
        speak(`${title}. ${pageDescription} ${contentText}`);
      }, 100);
    } else {
      // Combine title, description and content for a comprehensive reading
      speak(`${title}. ${pageDescription} ${contentText}`);
    }
  };

  // Improve open/close behavior for focus management
  const closeMenu = () => {
    setIsOpen(false);
    // Return focus to trigger button when closing
    setTimeout(() => {
      buttonRef.current?.focus();
    }, 0);
  };

  const openMenu = () => {
    setIsOpen(true);
    // Move focus to dialog heading when opening
    setTimeout(() => {
      document.getElementById('accessibility-menu-heading')?.focus();
    }, 100);
  };

  const toggleMenu = () => {
    if (isOpen) {
      closeMenu();
    } else {
      openMenu();
    }
  };

  // While menu is open, hide other content from assistive tech and make it inert
  useEffect(() => {
    if (typeof document === 'undefined') return;
    const container = document.getElementById('accessibility-controls-container');
    const siblings = Array.from(document.body.children);

    if (isOpen) {
      siblings.forEach((el) => {
        if (el === container) return;
        el.setAttribute('aria-hidden', 'true');
        try { el.setAttribute('inert', ''); } catch (e) {}
      });
    } else {
      siblings.forEach((el) => {
        if (el === container) return;
        el.removeAttribute('aria-hidden');
        el.removeAttribute('inert');
      });
    }

    return () => {
      siblings.forEach((el) => {
        if (el === container) return;
        el.removeAttribute('aria-hidden');
        el.removeAttribute('inert');
      });
    };
  }, [isOpen]);

  if (!mounted) return null;

  return (
    <div id="accessibility-controls-container" suppressHydrationWarning className="fixed z-50 right-[max(1rem,env(safe-area-inset-right))] bottom-[max(1rem,env(safe-area-inset-bottom))] md:right-8 md:bottom-auto md:top-1/2 md:-translate-y-1/2">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            {...animationConfig}
            transition={{ duration: settings.reducedMotion ? 0 : 0.3 }}
            className="w-full max-w-[calc(100vw-2rem)] md:max-w-sm p-5 mb-6 bg-white border border-gray-100 shadow-2xl rounded-2xl backdrop-blur-sm"
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.8)'
            }}
            id="accessibility-menu"
            role="dialog"
            aria-labelledby="accessibility-menu-heading"
            aria-describedby="accessibility-menu-desc"
            aria-modal="true"
            ref={menuRef}
          >
            {/* Visually hidden instructions for screen reader users */}
            <p id="accessibility-menu-desc" className="sr-only">
              Gunakan tombol Tab untuk navigasi, Shift + Tab untuk kembali, dan Escape untuk menutup menu.
            </p>

            <div className="flex items-center justify-between pb-2 mb-4 border-b">
              <h3 
                id="accessibility-menu-heading" 
                className="font-medium text-gray-800"
                tabIndex="-1"
              >
                Pengaturan Aksesibilitas
              </h3>
              <button 
                onClick={closeMenu}
                className="p-2 text-gray-500 transition-all duration-200 rounded-full hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-red-500"
                aria-label="Tutup menu aksesibilitas"
              >
                <X size={18} aria-hidden="true" />
              </button>
            </div>

            <div className="p-3 mb-4 rounded-lg bg-blue-50">
              <h4 className="mb-1 font-medium text-blue-700">Baca halaman ini</h4>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={handleReadPage}
                  className="flex items-center gap-1 px-3 py-1 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="Baca judul dan deskripsi halaman ini"
                >
                  <FileAudio size={16} aria-hidden="true" /> <span>Baca Halaman Ini</span>
                </button>
                {isSpeaking && (
                  <button
                    onClick={() => stopSpeaking()}
                    className="flex items-center gap-1 px-3 py-1 text-sm text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    aria-label="Hentikan pembacaan"
                  >
                    <VolumeX size={16} aria-hidden="true" /> <span>Hentikan</span>
                  </button>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              {/* Kolom pertama pengaturan */}
              <div className="space-y-2">
                <AccessibilityButton
                  icon={<Volume2 size={18} />}
                  inactiveIcon={<VolumeX size={18} />}
                  label="Text-to-Speech"
                  isActive={settings.textToSpeech}
                  onClick={() => toggleSetting('textToSpeech')}
                  colorActive="bg-primary-600"
                  description="Membaca teks saat dihover dengan mouse"
                />
                
                <AccessibilityButton
                  icon={<ZoomIn size={18} />}
                  label="Teks Besar"
                  isActive={settings.largeText}
                  onClick={() => toggleSetting('largeText')}
                  colorActive="bg-blue-600"
                  description="Memperbesar ukuran teks di seluruh situs"
                />

                <AccessibilityButton
                  icon={<Eye size={18} />}
                  label="Kontras Tinggi"
                  isActive={settings.highContrast}
                  onClick={() => toggleSetting('highContrast')}
                  colorActive="bg-primary-600"
                  description="Mode kontras tinggi untuk keterbacaan lebih baik"
                />
              </div>

              {/* Kolom kedua pengaturan */}
              <div className="space-y-2">
                <AccessibilityButton
                  icon={<Minus size={18} />}
                  label="Kurangi Animasi"
                  isActive={settings.reducedMotion}
                  onClick={() => toggleSetting('reducedMotion')}
                  colorActive="bg-blue-600"
                  description="Mengurangi atau menghilangkan animasi"
                />

                <AccessibilityButton
                  icon={<Highlighter size={18} />}
                  label="Mode Fokus"
                  isActive={settings.focusMode}
                  onClick={() => toggleSetting('focusMode')}
                  colorActive="bg-primary-600"
                  description="Meningkatkan fokus pada elemen yang aktif"
                />

                <AccessibilityButton
                  icon={<BookOpen size={18} />}
                  label="Font Disleksia"
                  isActive={settings.dyslexicFont}
                  onClick={() => toggleSetting('dyslexicFont')}
                  colorActive="bg-blue-600"
                  description="Menggunakan font yang lebih mudah dibaca untuk penyandang disleksia"
                />
              </div>
            </div>

            <div className="pt-3 mt-4 text-xs text-center text-gray-500 border-t" aria-live="polite">
              <p>Semua pengaturan aksesibilitas akan tersimpan otomatis untuk kunjungan berikutnya.</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        onClick={toggleMenu}
        whileHover={settings.reducedMotion ? {} : { scale: 1.1 }}
        whileTap={settings.reducedMotion ? {} : { scale: 0.95 }}
        animate={settings.reducedMotion ? {} : { 
          y: [0, -5, 0],
          transition: { 
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
        className="inline-flex items-center justify-center w-16 h-16 md:w-16 md:h-16 text-white transition-all duration-300 bg-red-600 border-4 border-white rounded-full shadow-2xl hover:bg-red-700 hover:shadow-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 backdrop-blur-sm"
        style={{
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.05), 0 0 20px rgba(220, 38, 38, 0.3)'
        }}
        title="Pengaturan Aksesibilitas untuk Penyandang Disabilitas"
        aria-label="Pengaturan Aksesibilitas untuk Penyandang Disabilitas"
        aria-expanded={isOpen}
        aria-controls="accessibility-menu"
        ref={buttonRef}
      >
        <Accessibility size={28} aria-hidden="true" />
      </motion.button>
    </div>
  );
}

// Component untuk tombol aksesibilitas
function AccessibilityButton({ icon, inactiveIcon, label, isActive, onClick, colorActive, description }) {
  return (
    <button
      onClick={onClick}
      className={`w-full flex items-center justify-between px-3 py-2 rounded-md transition-colors ${
        isActive ? `${colorActive} text-white` : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      aria-pressed={isActive}
      data-tts-enabled
      aria-label={`${label}: ${isActive ? 'aktif' : 'tidak aktif'}. ${description}`}
    >
      <span className="flex items-center gap-2">
        {isActive ? icon : inactiveIcon || icon}
        <span className="text-sm font-medium">{label}</span>
      </span>
      <span className="text-xs font-medium" aria-hidden="true">{isActive ? 'ON' : 'OFF'}</span>
    </button>
  );
}