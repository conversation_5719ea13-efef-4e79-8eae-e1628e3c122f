@echo off
REM PPID BPMP Deployment Script for Windows (Non-Docker)

echo 🚀 Starting PPID BPMP Deployment...

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
call npm ci
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo 🗄️ Generating Prisma client...
call npx prisma generate
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to generate Prisma client
    pause
    exit /b 1
)

echo 🏗️ Building application...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo 🔍 Verifying and copying required files...
call node verify-standalone.js
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Standalone verification failed
    pause
    exit /b 1
)

echo 📁 Checking standalone build...
if exist ".next\standalone" (
    echo ✅ Standalone build created successfully
    
    REM Create logs directory
    if not exist "logs" mkdir logs
    
    echo.
    echo 🎯 Deployment options:
    echo 1. Run with Node.js directly: npm run deploy:vps
    echo 2. Run with PM2 ^(recommended^): npm run deploy:pm2
    echo 3. Start manually: cd .next\standalone ^&^& node server.js
    
    echo.
    echo 🌐 Environment setup:
    echo - Make sure your .env file is configured
    echo - Database connection should be working
    echo - Port 3000 should be available ^(or set PORT env variable^)
    
    echo.
    echo ✅ Build completed successfully!
    echo 📍 Standalone files are in: .next\standalone\
    
) else (
    echo ❌ Standalone build failed
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
