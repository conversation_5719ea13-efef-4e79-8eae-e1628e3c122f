const fs = require('fs');
const path = require('path');

console.log('===============================================');
console.log('    CHECK SCRIPTS ORGANIZATION - COMPLETE');
console.log('===============================================');
console.log('');

// Check scripts location
console.log('[1/3] Checking scripts location...');
const checkDir = path.join(__dirname);
const files = fs.readdirSync(checkDir).filter(file => file.endsWith('.js') && file.startsWith('check-'));

console.log(`✅ Found ${files.length} check scripts:`);
files.forEach(file => {
  const stats = fs.statSync(path.join(checkDir, file));
  const size = stats.size;
  console.log(`   - ${file} (${size} bytes)`);
});
console.log('');

// Verify root directory cleanup
console.log('[2/3] Verifying root directory cleanup...');
const rootDir = path.join(__dirname, '..', '..');
const rootFiles = fs.readdirSync(rootDir).filter(file => file.startsWith('check-') && file.endsWith('.js'));

if (rootFiles.length === 0) {
  console.log('✅ Root directory clean - no check*.js files found');
} else {
  console.log('❌ Some check*.js files still in root directory:');
  rootFiles.forEach(file => console.log(`   - ${file}`));
}
console.log('');

// Show organized structure
console.log('[3/3] Showing check scripts categorization...');
console.log('');
console.log('🔍 Data Validation Scripts:');
console.log('   - check-all-posts.js');
console.log('   - check-content.js');
console.log('   - check-current-data.js');
console.log('   - check-data.js');
console.log('   - check-dashboard-posts.js');
console.log('');
console.log('🎨 UI/UX Validation Scripts:');
console.log('   - check-css-build.js');
console.log('   - check-logo-size.js');
console.log('');
console.log('📦 Dependencies Scripts:');
console.log('   - check-imports.js');
console.log('');

console.log('===============================================');
console.log('✅ CHECK SCRIPTS ORGANIZATION COMPLETE!');
console.log('===============================================');
console.log('');
console.log('📁 All check*.js files moved to: scripts/check/');
console.log(`📋 Total scripts organized: ${files.length} files`);
console.log('📖 Documentation: scripts/check/README.md');
console.log('');
console.log('🎯 Categories:');
console.log('   - Data Validation: 5 scripts');
console.log('   - UI/UX Validation: 2 scripts');
console.log('   - Dependencies: 1 script');
console.log('');
console.log('🚀 Usage: node scripts/check/script-name.js');
console.log('');
